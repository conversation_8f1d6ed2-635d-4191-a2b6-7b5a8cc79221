# Funding Rate Arbitrage System

A cryptocurrency funding rate arbitrage system built with NautilusTrader that monitors funding rates on Binance futures and executes arbitrage strategies between spot and futures markets.

## Features

- Real-time funding rate monitoring
- Automated arbitrage opportunity detection
- Risk management and position tracking
- Binance spot and futures integration
- Comprehensive backtesting framework
- Production-ready deployment configuration

## Quick Start

### 1. Environment Setup

This project uses [uv](https://docs.astral.sh/uv/) for Python package management.

```bash
# Clone the repository
git clone <repository-url>
cd funding_rate_arbitrage

# Install dependencies
uv sync

# Install development dependencies
uv sync --extra dev
```

### 2. Configuration

Copy the example environment file and configure your API keys:

```bash
cp .env.example .env
```

Edit `.env` file with your Binance API credentials:

```env
# Binance API Configuration
BINANCE_API_KEY=your_binance_spot_api_key_here
BINANCE_API_SECRET=your_binance_spot_api_secret_here
BINANCE_FUTURES_API_KEY=your_binance_futures_api_key_here
BINANCE_FUTURES_API_SECRET=your_binance_futures_api_secret_here

# Set to true for testing, false for live trading
BINANCE_TESTNET=true
```

### 3. Running Tests

```bash
# Run all tests
uv run python -m pytest

# Run specific test file
uv run python -m pytest tests/test_config.py -v

# Run with coverage
uv run python -m pytest --cov=src/funding_rate_arbitrage
```

### 4. Development

```bash
# Format code
uv run black src/ tests/

# Sort imports
uv run isort src/ tests/

# Type checking
uv run mypy src/

# Install pre-commit hooks
uv run pre-commit install
```

## Project Structure

```
funding_rate_arbitrage/
├── src/funding_rate_arbitrage/    # Main package
│   ├── __init__.py
│   └── config.py                  # Configuration management
├── tests/                         # Test files
├── config/                        # Configuration files
├── data/                          # Data storage
├── logs/                          # Log files
├── docs/                          # NautilusTrader documentation
├── .env.example                   # Environment template
├── pyproject.toml                 # Project configuration
└── README.md                      # This file
```

## Next Steps

1. **API Integration**: Verify Binance API connections
2. **Data Monitoring**: Implement funding rate data collection
3. **Strategy Development**: Build arbitrage detection algorithms
4. **Risk Management**: Implement position and risk controls
5. **Backtesting**: Validate strategies with historical data
6. **Production Deployment**: Deploy to live trading environment

## Documentation

- [NautilusTrader Documentation](./docs/)
- [Binance Integration Guide](./docs/integrations/binance.md)
- [Configuration Reference](./src/funding_rate_arbitrage/config.py)

## License

[Add your license here]