# Cache Usage Example

This example demonstrates how to use NautilusTrader's **Cache** system for storing and managing trading data and state.

**The strategy shows how to use Cache for:**

- Storing and retrieving **custom objects** for strategy state.
- Accessing **instrument information** and specifications.
- Managing **trading accounts** and their states.
- Tracking **orders** and **positions** in the system.

This helps you understand how to use Cache effectively in your trading strategies
for data management and state tracking. 🚀
