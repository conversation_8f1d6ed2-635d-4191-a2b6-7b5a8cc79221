# Minimal Reproducible Example

A bare-minimum template for reproducing and reporting issues with NautilusTrader.

## Why This Template?

- **Self-contained**: Includes artificial data generation - no need to attach market data files
- **Minimal**: Contains only essential components to demonstrate an issue
- **Simple and easy to modify**: Well-structured code that serves as a starting point for testing variations

## Using This Template

1. Copy these files
2. Modify the code to demonstrate your issue
3. Submit the modified version with your bug report

The artificial data generation makes it easy to share reproductions without needing to attach sensitive
or proprietary trading data.
