"""
多TradingNode架构示例 - 按功能分离
"""

import asyncio
from nautilus_trader.config import TradingNodeConfig, LoggingConfig
from nautilus_trader.live.node import TradingNode
from nautilus_trader.model.identifiers import TraderId
from nautilus_trader.adapters.binance import BINANCE, BinanceDataClientConfig, BinanceExecClientConfig
from nautilus_trader.adapters.binance import BinanceAccountType


class MultiNodeSystem:
    """多节点交易系统"""
    
    def __init__(self):
        self.data_node = None
        self.exec_node = None
        self.risk_node = None
    
    def create_data_node(self) -> TradingNode:
        """创建专门的数据节点"""
        config = TradingNodeConfig(
            trader_id=TraderId("DATA-NODE"),
            logging=LoggingConfig(log_level="INFO"),
            # 只配置数据客户端
            data_clients={
                BINANCE: BinanceDataClientConfig(
                    api_key="your_api_key",
                    api_secret="your_api_secret",
                    account_type=BinanceAccountType.SPOT,
                    testnet=True,
                ),
            },
            # 不配置执行客户端
            exec_clients={},
        )
        return TradingNode(config=config)
    
    def create_exec_node(self) -> TradingNode:
        """创建专门的执行节点"""
        config = TradingNodeConfig(
            trader_id=TraderId("EXEC-NODE"),
            logging=LoggingConfig(log_level="INFO"),
            # 不配置数据客户端（从共享存储获取数据）
            data_clients={},
            # 只配置执行客户端
            exec_clients={
                BINANCE: BinanceExecClientConfig(
                    api_key="your_api_key",
                    api_secret="your_api_secret",
                    account_type=BinanceAccountType.SPOT,
                    testnet=True,
                ),
            },
        )
        return TradingNode(config=config)
    
    def create_risk_node(self) -> TradingNode:
        """创建专门的风控节点"""
        config = TradingNodeConfig(
            trader_id=TraderId("RISK-NODE"),
            logging=LoggingConfig(log_level="INFO"),
            # 风控节点通常不需要直接的市场连接
            data_clients={},
            exec_clients={},
        )
        return TradingNode(config=config)
    
    async def start_system(self):
        """启动多节点系统"""
        print("🚀 启动多节点交易系统...")
        
        # 创建节点
        self.data_node = self.create_data_node()
        self.exec_node = self.create_exec_node()
        self.risk_node = self.create_risk_node()
        
        # 构建节点
        self.data_node.build()
        self.exec_node.build()
        self.risk_node.build()
        
        # 启动节点（按顺序）
        print("📊 启动数据节点...")
        await self.data_node.start_async()
        
        print("🎯 启动风控节点...")
        await self.risk_node.start_async()
        
        print("⚡ 启动执行节点...")
        await self.exec_node.start_async()
        
        print("✅ 所有节点启动完成！")
    
    async def stop_system(self):
        """停止多节点系统"""
        print("🛑 停止多节点交易系统...")
        
        # 按相反顺序停止
        if self.exec_node:
            await self.exec_node.stop_async()
            print("⚡ 执行节点已停止")
        
        if self.risk_node:
            await self.risk_node.stop_async()
            print("🎯 风控节点已停止")
        
        if self.data_node:
            await self.data_node.stop_async()
            print("📊 数据节点已停止")
        
        print("✅ 所有节点已停止！")


async def main():
    """主函数"""
    system = MultiNodeSystem()
    
    try:
        await system.start_system()
        
        # 运行一段时间
        print("🔄 系统运行中...")
        await asyncio.sleep(10)
        
    finally:
        await system.stop_system()


if __name__ == "__main__":
    asyncio.run(main())
