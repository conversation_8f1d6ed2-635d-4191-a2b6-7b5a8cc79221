"""
多交易所节点架构示例
"""

import asyncio
from nautilus_trader.config import TradingNodeConfig, LoggingConfig
from nautilus_trader.live.node import TradingNode
from nautilus_trader.model.identifiers import TraderId
from nautilus_trader.adapters.binance import BINANCE, BinanceDataClientConfig, BinanceExecClientConfig
from nautilus_trader.adapters.binance import BinanceAccountType


class MultiExchangeSystem:
    """多交易所系统"""
    
    def __init__(self):
        self.binance_spot_node = None
        self.binance_futures_node = None
        self.arbitrage_node = None
    
    def create_binance_spot_node(self) -> TradingNode:
        """创建Binance现货节点"""
        config = TradingNodeConfig(
            trader_id=TraderId("BINANCE-SPOT"),
            logging=LoggingConfig(log_level="INFO"),
            data_clients={
                BINANCE: BinanceDataClientConfig(
                    api_key="your_api_key",
                    api_secret="your_api_secret",
                    account_type=BinanceAccountType.SPOT,
                    testnet=True,
                ),
            },
            exec_clients={
                BINANCE: BinanceExecClientConfig(
                    api_key="your_api_key",
                    api_secret="your_api_secret",
                    account_type=BinanceAccountType.SPOT,
                    testnet=True,
                ),
            },
        )
        return TradingNode(config=config)
    
    def create_binance_futures_node(self) -> TradingNode:
        """创建Binance期货节点"""
        config = TradingNodeConfig(
            trader_id=TraderId("BINANCE-FUTURES"),
            logging=LoggingConfig(log_level="INFO"),
            data_clients={
                BINANCE: BinanceDataClientConfig(
                    api_key="your_futures_api_key",
                    api_secret="your_futures_api_secret",
                    account_type=BinanceAccountType.USDT_FUTURE,
                    testnet=True,
                ),
            },
            exec_clients={
                BINANCE: BinanceExecClientConfig(
                    api_key="your_futures_api_key",
                    api_secret="your_futures_api_secret",
                    account_type=BinanceAccountType.USDT_FUTURE,
                    testnet=True,
                ),
            },
        )
        return TradingNode(config=config)
    
    def create_arbitrage_node(self) -> TradingNode:
        """创建套利策略节点"""
        config = TradingNodeConfig(
            trader_id=TraderId("ARBITRAGE-STRATEGY"),
            logging=LoggingConfig(log_level="INFO"),
            # 策略节点不直接连接交易所
            # 而是从共享数据源获取数据，通过消息队列发送订单
            data_clients={},
            exec_clients={},
        )
        return TradingNode(config=config)
    
    async def start_system(self):
        """启动多交易所系统"""
        print("🚀 启动多交易所套利系统...")
        
        # 创建节点
        self.binance_spot_node = self.create_binance_spot_node()
        self.binance_futures_node = self.create_binance_futures_node()
        self.arbitrage_node = self.create_arbitrage_node()
        
        # 构建节点
        self.binance_spot_node.build()
        self.binance_futures_node.build()
        self.arbitrage_node.build()
        
        # 并行启动交易所节点
        await asyncio.gather(
            self.binance_spot_node.start_async(),
            self.binance_futures_node.start_async(),
        )
        print("📊 交易所节点启动完成")
        
        # 启动策略节点
        await self.arbitrage_node.start_async()
        print("🎯 套利策略节点启动完成")
        
        print("✅ 多交易所系统启动完成！")


async def main():
    """主函数"""
    system = MultiExchangeSystem()
    await system.start_system()


if __name__ == "__main__":
    asyncio.run(main())
