# 多源多策略交易系统配置

system:
  name: "multi-strategy-trading-system"
  version: "1.0.0"
  environment: "development"  # development, staging, production
  
# 数据节点配置
data_nodes:
  - name: "binance-spot-data-node"
    log_level: "INFO"
    log_directory: "logs"
    log_file_name: "binance_spot_data.log"
    timeout_connection: 10.0
    exchanges:
      binance:
        enabled: true
        testnet: true
        base_url_http: "https://testnet.binance.vision"
        base_url_ws: "wss://testnet.binance.vision"
        spot:
          enabled: true
          api_key: "${BINANCE_SPOT_API_KEY}"
          api_secret: "${BINANCE_SPOT_API_SECRET}"
        futures:
          enabled: false  # 暂时只启用现货
          api_key: "${BINANCE_FUTURES_API_KEY}"
          api_secret: "${BINANCE_FUTURES_API_SECRET}"

# 策略节点配置 (暂时禁用)
strategy_nodes: []
  # - name: "arbitrage-strategy-node"
  #   log_level: "INFO"
  #   strategies:
  #     arbitrage:
  #       spot_futures:
  #         enabled: true
  #         instrument_id: "BTCUSDT"
  #         exchange: "BINANCE"

# 执行节点配置 (暂时禁用)
execution_nodes: []
  # - name: "binance-execution-node"
  #   log_level: "INFO"
  #   exchanges:
  #     binance:
  #       enabled: true
  #       testnet: true
  #       spot:
  #         enabled: true

# 风控节点配置 (暂时禁用)
risk_nodes: []
  # - name: "main-risk-node"
  #   log_level: "INFO"
  #   position_monitor:
  #     enabled: true

# 监控配置
monitoring:
  enabled: true
  metrics:
    enabled: true
    prometheus:
      enabled: false
      port: 8000
    custom_metrics:
      enabled: true
  
  alerts:
    enabled: true
    channels:
      email:
        enabled: false
        smtp_server: "smtp.gmail.com"
        smtp_port: 587
        username: "${EMAIL_USERNAME}"
        password: "${EMAIL_PASSWORD}"
        recipients: ["<EMAIL>"]
      
      slack:
        enabled: false
        webhook_url: "${SLACK_WEBHOOK_URL}"
      
      telegram:
        enabled: false
        bot_token: "${TELEGRAM_BOT_TOKEN}"
        chat_id: "${TELEGRAM_CHAT_ID}"
  
  dashboard:
    enabled: false
    type: "streamlit"  # streamlit, grafana
    port: 8501

# 数据存储配置
storage:
  redis:
    enabled: false
    host: "localhost"
    port: 6379
    db: 0
    password: "${REDIS_PASSWORD}"
  
  timeseries:
    enabled: false
    type: "influxdb"  # influxdb, timescaledb
    host: "localhost"
    port: 8086
    database: "trading_data"
    username: "${INFLUXDB_USERNAME}"
    password: "${INFLUXDB_PASSWORD}"
  
  postgres:
    enabled: false
    host: "localhost"
    port: 5432
    database: "trading_system"
    username: "${POSTGRES_USERNAME}"
    password: "${POSTGRES_PASSWORD}"

# 日志配置
logging:
  level: "INFO"
  format: "{time} | {level} | {name} | {message}"
  to_file: true
  file_path: "logs/trading_system.log"
  max_file_size: "100MB"
  backup_count: 5
