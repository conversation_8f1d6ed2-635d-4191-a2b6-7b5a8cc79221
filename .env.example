# Binance API Configuration
# Copy this file to .env and fill in your actual API credentials

# Binance Spot API
BINANCE_API_KEY=your_binance_spot_api_key_here
BINANCE_API_SECRET=your_binance_spot_api_secret_here

# Binance Futures API (can be same as spot if using unified account)
BINANCE_FUTURES_API_KEY=your_binance_futures_api_key_here
BINANCE_FUTURES_API_SECRET=your_binance_futures_api_secret_here

# Trading Configuration
BINANCE_TESTNET=true  # Set to false for live trading
BINANCE_BASE_URL_HTTP=https://testnet.binance.vision  # Testnet URL
BINANCE_BASE_URL_WS=wss://testnet.binance.vision  # Testnet WebSocket

# Risk Management
MAX_POSITION_SIZE=1000  # Maximum position size in USDT
MAX_DAILY_LOSS=100     # Maximum daily loss in USDT
FUNDING_RATE_THRESHOLD=0.01  # Minimum funding rate to trigger arbitrage (1%)

# Logging
LOG_LEVEL=INFO
LOG_TO_FILE=true
