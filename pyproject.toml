[project]
name = "funding-rate-arbitrage"
version = "0.1.0"
description = "Cryptocurrency funding rate arbitrage system using NautilusTrader"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "nautilus_trader[binance]>=1.200.0",
    "pandas>=2.0.0",
    "numpy>=1.24.0",
    "python-dotenv>=1.0.0",
    "pydantic>=2.0.0",
    "asyncio-mqtt>=0.16.0",
    "redis>=5.0.0",
    "aiohttp>=3.8.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.5.0",
    "pre-commit>=3.0.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/funding_rate_arbitrage"]

[tool.black]
line-length = 88
target-version = ['py311']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
