"""
多源多策略交易系统启动脚本
"""

import asyncio
import logging
import signal
import sys
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from nodes.orchestrator import SystemOrchestrator
from config.config_loader import config_loader


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('trading_system.log')
    ]
)

logger = logging.getLogger(__name__)


class TradingSystemManager:
    """交易系统管理器"""
    
    def __init__(self):
        self.orchestrator = None
        self.shutdown_event = asyncio.Event()
        
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating shutdown...")
            self.shutdown_event.set()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def start_system(self):
        """启动交易系统"""
        try:
            logger.info("🚀 Initializing Multi-Strategy Trading System...")

            # 加载系统配置
            config = config_loader.load_system_config()

            # 验证API密钥
            if not config_loader.validate_api_keys(config):
                logger.error("❌ API keys validation failed. Please check your configuration.")
                return False

            # 创建系统编排器
            self.orchestrator = SystemOrchestrator(config['system'])

            # 注册数据节点
            data_nodes_config = config.get('data_nodes', [])
            if data_nodes_config:
                self.orchestrator.register_data_nodes(data_nodes_config)
                logger.info(f"Registered {len(data_nodes_config)} data nodes")

            # 注册策略节点
            strategy_nodes_config = config.get('strategy_nodes', [])
            if strategy_nodes_config:
                self.orchestrator.register_strategy_nodes(strategy_nodes_config)
                logger.info(f"Registered {len(strategy_nodes_config)} strategy nodes")

            # 注册执行节点
            execution_nodes_config = config.get('execution_nodes', [])
            if execution_nodes_config:
                self.orchestrator.register_execution_nodes(execution_nodes_config)
                logger.info(f"Registered {len(execution_nodes_config)} execution nodes")

            # 注册风控节点
            risk_nodes_config = config.get('risk_nodes', [])
            if risk_nodes_config:
                self.orchestrator.register_risk_nodes(risk_nodes_config)
                logger.info(f"Registered {len(risk_nodes_config)} risk nodes")
            
            # 启动系统
            success = await self.orchestrator.start_system()
            
            if success:
                logger.info("✅ Trading system started successfully!")
                
                # 显示系统状态
                await self.show_system_status()
                
                # 等待关闭信号
                logger.info("🔄 System is running... Press Ctrl+C to stop")
                await self.shutdown_event.wait()
                
            else:
                logger.error("❌ Failed to start trading system")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error starting system: {e}")
            return False
        
        finally:
            # 停止系统
            if self.orchestrator:
                logger.info("🛑 Shutting down trading system...")
                await self.orchestrator.stop_system()
                logger.info("✅ System shutdown complete")
        
        return True
    
    async def show_system_status(self):
        """显示系统状态"""
        if not self.orchestrator:
            return
            
        status = self.orchestrator.get_system_status()
        
        logger.info("📊 System Status:")
        logger.info(f"  Total Nodes: {status['total_nodes']}")
        logger.info(f"  Running Nodes: {status['running_nodes']}")
        logger.info(f"  System Health: {status['system_health']:.1%}")
        
        for node_type, type_status in status['nodes_by_type'].items():
            logger.info(f"  {node_type.upper()} Nodes: {type_status['running']}/{type_status['total']} running")
            for node_name, node_status in type_status['nodes'].items():
                status_icon = "✅" if node_status == "running" else "❌"
                logger.info(f"    {status_icon} {node_name}: {node_status}")
    
    async def monitor_system(self):
        """监控系统状态"""
        while not self.shutdown_event.is_set():
            try:
                await asyncio.sleep(30)  # 每30秒检查一次
                
                if self.orchestrator:
                    status = self.orchestrator.get_system_status()
                    
                    # 检查系统健康度
                    if status['system_health'] < 0.8:
                        logger.warning(f"⚠️ System health degraded: {status['system_health']:.1%}")
                    
                    # 检查是否有节点出错
                    for node_type, type_status in status['nodes_by_type'].items():
                        if type_status['error'] > 0:
                            logger.error(f"❌ {type_status['error']} {node_type} nodes in error state")
                
            except Exception as e:
                logger.error(f"Error in system monitor: {e}")


async def main():
    """主函数"""
    manager = TradingSystemManager()
    manager.setup_signal_handlers()
    
    # 启动系统监控任务
    monitor_task = asyncio.create_task(manager.monitor_system())
    
    try:
        # 启动交易系统
        success = await manager.start_system()
        
        if not success:
            logger.error("Failed to start trading system")
            return 1
            
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return 1
    finally:
        # 取消监控任务
        monitor_task.cancel()
        try:
            await monitor_task
        except asyncio.CancelledError:
            pass
    
    return 0


if __name__ == "__main__":
    # 运行主函数
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
