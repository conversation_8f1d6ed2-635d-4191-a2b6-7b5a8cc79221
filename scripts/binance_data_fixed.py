"""
修复后的Binance数据订阅 - 基于官方示例
"""

import asyncio
import logging
import signal
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
import sys
sys.path.insert(0, str(project_root / "src"))

from nautilus_trader.config import TradingNodeConfig, LoggingConfig, InstrumentProviderConfig
from nautilus_trader.live.node import TradingNode
from nautilus_trader.model.identifiers import TraderId, InstrumentId, StrategyId
from nautilus_trader.model.venues import Venue
from nautilus_trader.adapters.binance import BinanceDataClientConfig, BinanceAccountType
from nautilus_trader.adapters.binance import BinanceLiveDataClientFactory
from nautilus_trader.model.data import QuoteTick, TradeTick
from nautilus_trader.trading.strategy import Strategy


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DataSubscriberStrategy(Strategy):
    """简单的数据订阅策略"""

    def __init__(self, instrument_ids: list[InstrumentId]):
        super().__init__(config=None)
        self.instrument_ids = instrument_ids
        self.data_count = 0

    def on_start(self) -> None:
        """策略启动时订阅数据"""
        self.log.info("🚀 Starting data subscription strategy...")

        for instrument_id in self.instrument_ids:
            self.log.info(f"📡 Subscribing to {instrument_id}...")

            # 订阅报价数据
            self.subscribe_quote_ticks(instrument_id)

            # 订阅交易数据
            self.subscribe_trade_ticks(instrument_id)

            self.log.info(f"✅ Subscribed to {instrument_id}")

    def on_quote_tick(self, tick: QuoteTick) -> None:
        """处理报价数据"""
        self.data_count += 1
        if self.data_count % 10 == 0:  # 每10个tick显示一次
            self.log.info(f"📊 Quote #{self.data_count}: {tick.instrument_id} - "
                         f"Bid: {tick.bid_price} Ask: {tick.ask_price}")

    def on_trade_tick(self, tick: TradeTick) -> None:
        """处理交易数据"""
        self.data_count += 1
        if self.data_count % 5 == 0:  # 每5个tick显示一次
            self.log.info(f"💰 Trade #{self.data_count}: {tick.instrument_id} - "
                         f"Price: {tick.price} Size: {tick.size} Side: {tick.aggressor_side}")


class BinanceDataMonitor:
    """Binance数据监控器 - 修复版本"""
    
    def __init__(self):
        self.node = None
        self.strategy = None
        self.shutdown_event = asyncio.Event()
        
    def create_node_config(self) -> TradingNodeConfig:
        """创建节点配置 - 基于官方示例"""
        return TradingNodeConfig(
            trader_id=TraderId("BINANCE-DATA-MONITOR"),
            logging=LoggingConfig(
                log_level="INFO",
                use_pyo3=True,
            ),
            data_clients={
                "BINANCE_SPOT": BinanceDataClientConfig(
                    venue=Venue("BINANCE_SPOT"),
                    api_key="2s39JuJ3j4egAQObcRhIwAKKqtlpb4hl6UcPlBFjajjd27ilMG3Ye3wZdjcxpoCb",
                    api_secret="2BaaJLjZSgJjFIe4ALNcbhokksRYx9JvrdAQLElVW9Hh9TUw8iv006riUait9xPZ",
                    account_type=BinanceAccountType.SPOT,
                    base_url_http="https://testnet.binance.vision",
                    base_url_ws="wss://testnet.binance.vision",
                    testnet=True,
                    us=False,
                    # 关键：正确配置交易对加载
                    instrument_provider=InstrumentProviderConfig(
                        load_all=False,  # 不加载所有交易对
                        load_ids=(
                            InstrumentId.from_str("BTCUSDT.BINANCE_SPOT"),
                            InstrumentId.from_str("ETHUSDT.BINANCE_SPOT"),
                            InstrumentId.from_str("BNBUSDT.BINANCE_SPOT"),
                        )
                    ),
                ),
            },
            exec_clients={},  # 只需要数据，不需要执行
            timeout_connection=30.0,
            timeout_reconciliation=10.0,
            timeout_portfolio=10.0,
            timeout_disconnection=10.0,
        )
    

    
    async def start_monitoring(self):
        """开始监控"""
        try:
            logger.info("🚀 Starting Binance data monitoring...")
            
            # 创建节点配置
            config = self.create_node_config()
            
            # 创建交易节点
            self.node = TradingNode(config=config)
            
            # 创建数据订阅策略
            instruments = await self.get_instruments()
            if instruments:
                self.strategy = DataSubscriberStrategy(instrument_ids=instruments)
                self.node.trader.add_strategy(self.strategy)

            # 注册客户端工厂
            self.node.add_data_client_factory("BINANCE_SPOT", BinanceLiveDataClientFactory)

            # 构建节点
            self.node.build()
            
            # 在后台启动节点
            run_task = asyncio.create_task(self.node.run_async())
            
            # 等待节点启动
            await asyncio.sleep(5)
            
            if self.node.is_running:
                logger.info("✅ Node started successfully!")
                logger.info("🔄 Monitoring data... Press Ctrl+C to stop")

                # 等待关闭信号
                await self.shutdown_event.wait()
                
            else:
                logger.error("❌ Failed to start node")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error in monitoring: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        finally:
            # 停止节点
            if self.node and self.node.is_running:
                logger.info("🛑 Stopping node...")
                self.node.stop()
                
                # 等待运行任务完成
                if 'run_task' in locals() and not run_task.done():
                    run_task.cancel()
                    try:
                        await run_task
                    except asyncio.CancelledError:
                        pass
                
                self.node.dispose()
                logger.info("✅ Node stopped")
        
        return True



    async def get_instruments(self):
        """获取交易对列表"""
        # 返回预配置的交易对
        return [
            InstrumentId.from_str("ETHUSDT.BINANCE_SPOT"),
            InstrumentId.from_str("BNBUSDT.BINANCE_SPOT"),
            InstrumentId.from_str("BTCUSDT.BINANCE_SPOT"),
        ]
    
    async def show_statistics(self):
        """显示统计信息"""
        while not self.shutdown_event.is_set():
            await asyncio.sleep(10)  # 每10秒显示一次统计
            
            if self.node and self.node.is_running and self.strategy:
                logger.info(f"📈 Total data received: {self.strategy.data_count} ticks")
                
                # 显示缓存中的数据
                cache = self.node.kernel.cache
                instruments = cache.instruments()
                
                if instruments:
                    logger.info(f"📊 Active instruments: {len(instruments)}")
                    for instrument in instruments:
                        quotes = cache.quote_ticks(instrument.id)
                        trades = cache.trade_ticks(instrument.id)
                        logger.info(f"  {instrument.id}: {len(quotes)} quotes, {len(trades)} trades")


async def main():
    """主函数"""
    monitor = BinanceDataMonitor()
    
    # 设置信号处理器
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}, stopping...")
        monitor.shutdown_event.set()
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 启动统计任务
    stats_task = asyncio.create_task(monitor.show_statistics())
    
    try:
        # 启动监控
        success = await monitor.start_monitoring()
        
        if not success:
            logger.error("Failed to start monitoring")
            return 1
            
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return 1
    finally:
        # 取消统计任务
        stats_task.cancel()
        try:
            await stats_task
        except asyncio.CancelledError:
            pass
    
    return 0


if __name__ == "__main__":
    # 运行主函数
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
