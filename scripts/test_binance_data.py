"""
测试Binance数据接入
"""

import asyncio
import logging
import signal
import sys
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from nautilus_trader.config import TradingNodeConfig
from nautilus_trader.live.node import TradingNode
from nautilus_trader.model.identifiers import TraderId
from nautilus_trader.adapters.binance import BINANCE, BinanceDataClientConfig, BinanceAccountType
from nautilus_trader.adapters.binance import BinanceLiveDataClientFactory
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.data import QuoteTick, TradeTick
from nautilus_trader.core.message import Event


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('binance_data_test.log')
    ]
)

logger = logging.getLogger(__name__)


class BinanceDataMonitor:
    """Binance数据监控器"""
    
    def __init__(self):
        self.node = None
        self.shutdown_event = asyncio.Event()
        self.data_count = 0
        
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating shutdown...")
            self.shutdown_event.set()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def create_node_config(self) -> TradingNodeConfig:
        """创建节点配置"""
        return TradingNodeConfig(
            trader_id=TraderId("BINANCE-DATA-MONITOR"),
            data_clients={
                "BINANCE-SPOT": BinanceDataClientConfig(
                    api_key="2s39JuJ3j4egAQObcRhIwAKKqtlpb4hl6UcPlBFjajjd27ilMG3Ye3wZdjcxpoCb",
                    api_secret="2BaaJLjZSgJjFIe4ALNcbhokksRYx9JvrdAQLElVW9Hh9TUw8iv006riUait9xPZ",
                    account_type=BinanceAccountType.SPOT,
                    base_url_http="https://testnet.binance.vision",
                    base_url_ws="wss://testnet.binance.vision",
                    testnet=True,
                    us=False,
                ),
            },
            exec_clients={},
        )
    
    def on_quote_tick(self, tick: QuoteTick):
        """处理报价数据"""
        self.data_count += 1
        logger.info(f"📊 Quote Tick #{self.data_count}: {tick.instrument_id} - "
                   f"Bid: {tick.bid_price} Ask: {tick.ask_price} "
                   f"Time: {tick.ts_event}")
    
    def on_trade_tick(self, tick: TradeTick):
        """处理交易数据"""
        self.data_count += 1
        logger.info(f"💰 Trade Tick #{self.data_count}: {tick.instrument_id} - "
                   f"Price: {tick.price} Size: {tick.size} "
                   f"Side: {tick.aggressor_side} Time: {tick.ts_event}")
    
    def on_event(self, event: Event):
        """处理其他事件"""
        logger.info(f"📢 Event: {type(event).__name__} - {event}")
    
    async def start_monitoring(self):
        """开始监控"""
        try:
            logger.info("🚀 Starting Binance Data Monitor...")
            
            # 创建节点配置
            config = self.create_node_config()
            
            # 创建交易节点
            self.node = TradingNode(config=config)
            
            # 添加Binance数据客户端工厂
            self.node.add_data_client_factory(BINANCE, BinanceLiveDataClientFactory)
            
            # 构建节点
            self.node.build()
            
            # 订阅数据事件
            self.node.kernel.msgbus.subscribe(
                topic="data.quotes.*",
                handler=self.on_quote_tick
            )
            
            self.node.kernel.msgbus.subscribe(
                topic="data.trades.*",
                handler=self.on_trade_tick
            )
            
            # 在后台启动节点
            run_task = asyncio.create_task(self.node.run_async())
            
            # 等待节点启动
            await asyncio.sleep(3)
            
            if self.node.is_running:
                logger.info("✅ Node started successfully!")
                
                # 订阅一些热门交易对的数据
                await self.subscribe_to_instruments()
                
                # 等待关闭信号
                logger.info("🔄 Monitoring data... Press Ctrl+C to stop")
                await self.shutdown_event.wait()
                
            else:
                logger.error("❌ Failed to start node")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error in monitoring: {e}")
            return False
        
        finally:
            # 停止节点
            if self.node and self.node.is_running:
                logger.info("🛑 Stopping node...")
                self.node.stop()
                
                # 等待运行任务完成
                if 'run_task' in locals() and not run_task.done():
                    run_task.cancel()
                    try:
                        await run_task
                    except asyncio.CancelledError:
                        pass
                
                self.node.dispose()
                logger.info("✅ Node stopped")
        
        return True
    
    async def subscribe_to_instruments(self):
        """订阅交易对数据"""
        # 定义要监控的交易对
        instruments = [
            "BTCUSDT",
            "ETHUSDT", 
            "BNBUSDT",
        ]
        
        logger.info(f"📡 Subscribing to {len(instruments)} instruments...")
        
        for symbol in instruments:
            try:
                instrument_id = InstrumentId.from_str(f"{symbol}.BINANCE")
                
                # 订阅报价数据
                self.node.kernel.data_engine.subscribe_quote_ticks(instrument_id)
                
                # 订阅交易数据
                self.node.kernel.data_engine.subscribe_trade_ticks(instrument_id)
                
                logger.info(f"✅ Subscribed to {symbol}")
                
                # 避免订阅过快
                await asyncio.sleep(0.5)
                
            except Exception as e:
                logger.error(f"❌ Failed to subscribe to {symbol}: {e}")
    
    async def show_statistics(self):
        """显示统计信息"""
        while not self.shutdown_event.is_set():
            await asyncio.sleep(10)  # 每10秒显示一次统计
            
            if self.node and self.node.is_running:
                logger.info(f"📈 Data Statistics: {self.data_count} ticks received")
                
                # 显示缓存中的数据
                cache = self.node.kernel.cache
                instruments = cache.instruments()
                logger.info(f"📊 Cached instruments: {len(instruments)}")
                
                for instrument in list(instruments)[:5]:  # 只显示前5个
                    quotes = cache.quote_ticks(instrument.id)
                    trades = cache.trade_ticks(instrument.id)
                    logger.info(f"  {instrument.id}: {len(quotes)} quotes, {len(trades)} trades")


async def main():
    """主函数"""
    monitor = BinanceDataMonitor()
    monitor.setup_signal_handlers()
    
    # 启动统计任务
    stats_task = asyncio.create_task(monitor.show_statistics())
    
    try:
        # 启动监控
        success = await monitor.start_monitoring()
        
        if not success:
            logger.error("Failed to start monitoring")
            return 1
            
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return 1
    finally:
        # 取消统计任务
        stats_task.cancel()
        try:
            await stats_task
        except asyncio.CancelledError:
            pass
    
    return 0


if __name__ == "__main__":
    # 运行主函数
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
