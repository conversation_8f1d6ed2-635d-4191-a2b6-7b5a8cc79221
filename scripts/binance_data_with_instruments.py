"""
测试Binance数据接入 - 包含交易对加载
"""

import asyncio
import logging
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
import sys
sys.path.insert(0, str(project_root / "src"))

from nautilus_trader.config import TradingNodeConfig
from nautilus_trader.live.node import TradingNode
from nautilus_trader.model.identifiers import TraderId, InstrumentId
from nautilus_trader.adapters.binance import BINANCE, BinanceDataClientConfig, BinanceAccountType
from nautilus_trader.adapters.binance import BinanceLiveDataClientFactory


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_binance_with_instruments():
    """测试Binance连接并加载交易对"""
    
    logger.info("🚀 Testing Binance connection with instruments...")
    
    # 创建节点配置 - 使用默认配置
    config = TradingNodeConfig(
        trader_id=TraderId("BINANCE-INSTRUMENTS-TEST"),
        data_clients={
            "BINANCE-SPOT": BinanceDataClientConfig(
                api_key="2s39JuJ3j4egAQObcRhIwAKKqtlpb4hl6UcPlBFjajjd27ilMG3Ye3wZdjcxpoCb",
                api_secret="2BaaJLjZSgJjFIe4ALNcbhokksRYx9JvrdAQLElVW9Hh9TUw8iv006riUait9xPZ",
                account_type=BinanceAccountType.SPOT,
                base_url_http="https://testnet.binance.vision",
                base_url_ws="wss://testnet.binance.vision",
                testnet=True,
                us=False,
                update_instruments_interval_mins=60,  # 每小时更新一次交易对
            ),
        },
        exec_clients={},
    )
    
    # 创建交易节点
    node = TradingNode(config=config)
    
    # 添加Binance数据客户端工厂
    node.add_data_client_factory(BINANCE, BinanceLiveDataClientFactory)
    
    try:
        # 构建节点
        logger.info("📦 Building node...")
        node.build()
        
        # 启动节点
        logger.info("🔌 Starting node...")
        run_task = asyncio.create_task(node.run_async())
        
        # 等待节点启动
        await asyncio.sleep(5)
        
        if node.is_running:
            logger.info("✅ Node is running!")
            
            # 获取缓存中的交易对
            cache = node.kernel.cache
            instruments = cache.instruments()
            logger.info(f"🎯 Available instruments: {len(instruments)}")
            
            if instruments:
                for instrument in list(instruments)[:5]:  # 只显示前5个
                    logger.info(f"  📈 {instrument.id}: {instrument.native_symbol}")
                    logger.info(f"      Price precision: {instrument.price_precision}")
                    logger.info(f"      Size precision: {instrument.size_precision}")
                    logger.info(f"      Min quantity: {instrument.min_quantity}")
                    logger.info(f"      Max quantity: {instrument.max_quantity}")
                    logger.info(f"      Tick size: {instrument.price_increment}")
                    logger.info("")

                # 订阅数据
                await test_data_subscription(node, instruments)

            else:
                logger.warning("⚠️ No instruments found in cache")
                logger.info("🔄 Attempting to load instruments...")

                # 尝试加载交易对
                instruments = await load_instruments(node)

                if instruments:
                    logger.info(f"✅ Loaded {len(instruments)} instruments")
                    # 显示前几个交易对
                    for instrument in list(instruments)[:5]:
                        logger.info(f"  📈 {instrument.id}: {instrument.native_symbol}")

                    # 订阅数据
                    await test_data_subscription(node, instruments)
                else:
                    logger.error("❌ Failed to load instruments")
            
            # 运行一段时间
            logger.info("⏰ Running for 20 seconds...")
            await asyncio.sleep(20)
            
        else:
            logger.error("❌ Node failed to start")
            
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 停止节点
        if node.is_running:
            logger.info("🛑 Stopping node...")
            node.stop()
            
            # 等待运行任务完成
            if 'run_task' in locals() and not run_task.done():
                run_task.cancel()
                try:
                    await run_task
                except asyncio.CancelledError:
                    pass
            
            node.dispose()
            logger.info("✅ Node stopped")


async def load_instruments(node):
    """加载交易对到缓存"""
    logger.info("🔄 Loading instruments from Binance...")

    try:
        # 获取数据客户端
        data_clients = node.kernel.data_engine.registered_clients
        binance_client = None

        for client in data_clients:
            if "BINANCE" in str(client):
                binance_client = client
                break

        if not binance_client:
            logger.error("❌ Binance client not found")
            return []

        # 请求加载特定的热门交易对
        popular_symbols = [
            "BTCUSDT.BINANCE",
            "ETHUSDT.BINANCE",
            "BNBUSDT.BINANCE",
            "ADAUSDT.BINANCE",
            "SOLUSDT.BINANCE"
        ]

        logger.info(f"📡 Requesting {len(popular_symbols)} popular instruments...")

        # 尝试通过客户端加载交易对
        try:
            # 获取Binance数据客户端的instrument provider
            if hasattr(binance_client, '_instrument_provider'):
                provider = binance_client._instrument_provider
                logger.info("🔄 Loading instruments via provider...")

                # 设置要加载的交易对
                provider._load_ids = {InstrumentId.from_str(symbol) for symbol in popular_symbols}

                # 加载交易对
                if hasattr(provider, 'load_ids_async'):
                    await provider.load_ids_async()
                elif hasattr(provider, 'load_all_async'):
                    await provider.load_all_async()

                await asyncio.sleep(2)

        except Exception as e:
            logger.warning(f"⚠️ Failed to load via provider: {e}")

        # 等待交易对加载
        logger.info("⏳ Waiting for instruments to load...")
        await asyncio.sleep(3)

        # 检查缓存中的交易对
        cache = node.kernel.cache
        instruments = cache.instruments()

        if instruments:
            logger.info(f"✅ Successfully loaded {len(instruments)} instruments")
            return instruments
        else:
            logger.warning("⚠️ No instruments loaded, trying alternative method...")

            # 尝试直接从客户端获取交易对信息
            # 这需要访问客户端的内部方法
            try:
                # 获取Binance数据客户端的instrument provider
                if hasattr(binance_client, '_instrument_provider'):
                    provider = binance_client._instrument_provider
                    if hasattr(provider, 'load_all_async'):
                        logger.info("🔄 Loading all instruments...")
                        await provider.load_all_async()
                        await asyncio.sleep(2)

                        # 再次检查缓存
                        instruments = cache.instruments()
                        if instruments:
                            logger.info(f"✅ Loaded {len(instruments)} instruments via provider")
                            return instruments

            except Exception as e:
                logger.error(f"❌ Error loading via provider: {e}")

            return []

    except Exception as e:
        logger.error(f"❌ Error loading instruments: {e}")
        return []


async def test_data_subscription(node, instruments):
    """测试数据订阅"""
    logger.info("📡 Testing data subscription...")

    if not instruments:
        logger.warning("⚠️ No instruments available for subscription")
        return

    # 选择前3个交易对进行订阅
    selected_instruments = list(instruments)[:3]
    logger.info(f"📊 Subscribing to {len(selected_instruments)} instruments...")

    for instrument in selected_instruments:
        try:
            logger.info(f"📡 Subscribing to {instrument.id}...")

            # 订阅报价数据
            node.kernel.data_engine.subscribe_quote_ticks(instrument.id)
            logger.info(f"✅ Subscribed to quote ticks for {instrument.id}")

            # 订阅交易数据
            node.kernel.data_engine.subscribe_trade_ticks(instrument.id)
            logger.info(f"✅ Subscribed to trade ticks for {instrument.id}")

            # 避免订阅过快
            await asyncio.sleep(0.5)

        except Exception as e:
            logger.error(f"❌ Error subscribing to {instrument.id}: {e}")

    # 等待一段时间收集数据
    logger.info("⏳ Waiting for data...")
    await asyncio.sleep(10)

    # 检查收到的数据
    logger.info("📊 Data Summary:")
    total_quotes = 0
    total_trades = 0

    for instrument in selected_instruments:
        try:
            quotes = node.kernel.cache.quote_ticks(instrument.id)
            trades = node.kernel.cache.trade_ticks(instrument.id)

            total_quotes += len(quotes)
            total_trades += len(trades)

            logger.info(f"  {instrument.id}: {len(quotes)} quotes, {len(trades)} trades")

            # 显示最新数据
            if quotes:
                latest_quote = quotes[-1]
                logger.info(f"    📊 Latest quote: Bid={latest_quote.bid_price} Ask={latest_quote.ask_price}")

            if trades:
                latest_trade = trades[-1]
                logger.info(f"    💰 Latest trade: Price={latest_trade.price} Size={latest_trade.size}")

        except Exception as e:
            logger.error(f"❌ Error checking data for {instrument.id}: {e}")

    logger.info(f"📈 Total received: {total_quotes} quotes, {total_trades} trades")


if __name__ == "__main__":
    asyncio.run(test_binance_with_instruments())
