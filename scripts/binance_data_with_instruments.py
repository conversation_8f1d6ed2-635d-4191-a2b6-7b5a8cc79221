"""
测试Binance数据接入 - 包含交易对加载
"""

import asyncio
import logging
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
import sys
sys.path.insert(0, str(project_root / "src"))

from nautilus_trader.config import TradingNodeConfig
from nautilus_trader.live.node import TradingNode
from nautilus_trader.model.identifiers import TraderId, InstrumentId
from nautilus_trader.adapters.binance import BINANCE, BinanceDataClientConfig, BinanceAccountType
from nautilus_trader.adapters.binance import BinanceLiveDataClientFactory


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_binance_with_instruments():
    """测试Binance连接并加载交易对"""
    
    logger.info("🚀 Testing Binance connection with instruments...")
    
    # 创建节点配置
    config = TradingNodeConfig(
        trader_id=TraderId("BINANCE-INSTRUMENTS-TEST"),
        data_clients={
            "BINANCE-SPOT": BinanceDataClientConfig(
                api_key="2s39JuJ3j4egAQObcRhIwAKKqtlpb4hl6UcPlBFjajjd27ilMG3Ye3wZdjcxpoCb",
                api_secret="2BaaJLjZSgJjFIe4ALNcbhokksRYx9JvrdAQLElVW9Hh9TUw8iv006riUait9xPZ",
                account_type=BinanceAccountType.SPOT,
                base_url_http="https://testnet.binance.vision",
                base_url_ws="wss://testnet.binance.vision",
                testnet=True,
                us=False,
                update_instruments_interval_mins=60,  # 每小时更新一次交易对
            ),
        },
        exec_clients={},
    )
    
    # 创建交易节点
    node = TradingNode(config=config)
    
    # 添加Binance数据客户端工厂
    node.add_data_client_factory(BINANCE, BinanceLiveDataClientFactory)
    
    try:
        # 构建节点
        logger.info("📦 Building node...")
        node.build()
        
        # 启动节点
        logger.info("🔌 Starting node...")
        run_task = asyncio.create_task(node.run_async())
        
        # 等待节点启动
        await asyncio.sleep(5)
        
        if node.is_running:
            logger.info("✅ Node is running!")
            
            # 获取缓存中的交易对
            cache = node.kernel.cache
            instruments = cache.instruments()
            logger.info(f"🎯 Available instruments: {len(instruments)}")
            
            if instruments:
                for instrument in instruments:
                    logger.info(f"  📈 {instrument.id}: {instrument.native_symbol}")
                    logger.info(f"      Price precision: {instrument.price_precision}")
                    logger.info(f"      Size precision: {instrument.size_precision}")
                    logger.info(f"      Min quantity: {instrument.min_quantity}")
                    logger.info(f"      Max quantity: {instrument.max_quantity}")
                    logger.info(f"      Tick size: {instrument.price_increment}")
                    logger.info("")
                
                # 尝试订阅一些数据
                await test_data_subscription(node, instruments)
                
            else:
                logger.warning("⚠️ No instruments found in cache")
            
            # 运行一段时间
            logger.info("⏰ Running for 20 seconds...")
            await asyncio.sleep(20)
            
        else:
            logger.error("❌ Node failed to start")
            
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 停止节点
        if node.is_running:
            logger.info("🛑 Stopping node...")
            node.stop()
            
            # 等待运行任务完成
            if 'run_task' in locals() and not run_task.done():
                run_task.cancel()
                try:
                    await run_task
                except asyncio.CancelledError:
                    pass
            
            node.dispose()
            logger.info("✅ Node stopped")


async def test_data_subscription(node, instruments):
    """测试数据订阅"""
    logger.info("📡 Testing data subscription...")
    
    # 订阅第一个交易对的数据
    if instruments:
        instrument = list(instruments)[0]
        logger.info(f"📊 Subscribing to {instrument.id}...")
        
        try:
            # 订阅报价数据
            node.kernel.data_engine.subscribe_quote_ticks(instrument.id)
            logger.info(f"✅ Subscribed to quote ticks for {instrument.id}")
            
            # 订阅交易数据
            node.kernel.data_engine.subscribe_trade_ticks(instrument.id)
            logger.info(f"✅ Subscribed to trade ticks for {instrument.id}")
            
            # 等待一段时间收集数据
            await asyncio.sleep(5)
            
            # 检查收到的数据
            quotes = node.kernel.cache.quote_ticks(instrument.id)
            trades = node.kernel.cache.trade_ticks(instrument.id)
            
            logger.info(f"📈 Received {len(quotes)} quote ticks")
            logger.info(f"💰 Received {len(trades)} trade ticks")
            
            # 显示最新的几个数据点
            if quotes:
                latest_quote = quotes[-1]
                logger.info(f"📊 Latest quote: Bid={latest_quote.bid_price} Ask={latest_quote.ask_price}")
            
            if trades:
                latest_trade = trades[-1]
                logger.info(f"💰 Latest trade: Price={latest_trade.price} Size={latest_trade.size}")
                
        except Exception as e:
            logger.error(f"❌ Error subscribing to data: {e}")


if __name__ == "__main__":
    asyncio.run(test_binance_with_instruments())
