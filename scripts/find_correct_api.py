"""
查找正确的NautilusTrader API
"""

import asyncio
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
import sys
sys.path.insert(0, str(project_root / "src"))

from nautilus_trader.config import TradingNodeConfig
from nautilus_trader.live.node import TradingNode
from nautilus_trader.model.identifiers import TraderId
from nautilus_trader.adapters.binance import BINANCE, BinanceDataClientConfig, BinanceAccountType
from nautilus_trader.adapters.binance import BinanceLiveDataClientFactory


async def explore_api():
    """探索API"""
    
    print("🔍 Exploring NautilusTrader API...")
    
    # 创建节点配置
    config = TradingNodeConfig(
        trader_id=TraderId("API-EXPLORER"),
        data_clients={
            "BINANCE-SPOT": BinanceDataClientConfig(
                api_key="2s39JuJ3j4egAQObcRhIwAKKqtlpb4hl6UcPlBFjajjd27ilMG3Ye3wZdjcxpoCb",
                api_secret="2BaaJLjZSgJjFIe4ALNcbhokksRYx9JvrdAQLElVW9Hh9TUw8iv006riUait9xPZ",
                account_type=BinanceAccountType.SPOT,
                base_url_http="https://testnet.binance.vision",
                base_url_ws="wss://testnet.binance.vision",
                testnet=True,
                us=False,
            ),
        },
        exec_clients={},
    )
    
    # 创建交易节点
    node = TradingNode(config=config)
    
    # 添加Binance数据客户端工厂
    node.add_data_client_factory(BINANCE, BinanceLiveDataClientFactory)
    
    try:
        # 构建节点
        node.build()
        
        # 在后台启动节点
        run_task = asyncio.create_task(node.run_async())
        
        # 等待节点启动
        await asyncio.sleep(3)
        
        if node.is_running:
            print("✅ Node started successfully!")
            
            # 探索数据引擎API
            data_engine = node.kernel.data_engine
            print(f"\n📊 DataEngine type: {type(data_engine)}")
            print("📊 DataEngine methods:")
            
            methods = [method for method in dir(data_engine) if not method.startswith('_')]
            for method in sorted(methods):
                if 'subscribe' in method.lower():
                    print(f"  🔔 {method}")
                elif 'request' in method.lower():
                    print(f"  📡 {method}")
                elif 'data' in method.lower():
                    print(f"  📈 {method}")
            
            # 探索消息总线API
            msgbus = node.kernel.msgbus
            print(f"\n📨 MessageBus type: {type(msgbus)}")
            print("📨 MessageBus methods:")
            
            methods = [method for method in dir(msgbus) if not method.startswith('_')]
            for method in sorted(methods):
                if 'subscribe' in method.lower():
                    print(f"  🔔 {method}")
                elif 'publish' in method.lower():
                    print(f"  📤 {method}")
            
            # 探索数据客户端
            data_clients = data_engine.registered_clients
            print(f"\n🔗 Registered data clients: {len(data_clients)}")
            
            for client in data_clients:
                print(f"  📡 {client}")
                print(f"     Type: {type(client)}")
                
                # 查看客户端方法
                client_methods = [method for method in dir(client) if not method.startswith('_')]
                subscribe_methods = [m for m in client_methods if 'subscribe' in m.lower()]
                if subscribe_methods:
                    print(f"     Subscribe methods: {subscribe_methods}")
        
        else:
            print("❌ Node failed to start")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 停止节点
        if node.is_running:
            print("\n🛑 Stopping node...")
            node.stop()
            
            # 等待运行任务完成
            if 'run_task' in locals() and not run_task.done():
                run_task.cancel()
                try:
                    await run_task
                except asyncio.CancelledError:
                    pass
            
            node.dispose()
            print("✅ Node stopped")


if __name__ == "__main__":
    asyncio.run(explore_api())
