"""
简单的Binance数据测试
"""

import asyncio
import logging
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
import sys
sys.path.insert(0, str(project_root / "src"))

from nautilus_trader.config import TradingNodeConfig
from nautilus_trader.live.node import TradingNode
from nautilus_trader.model.identifiers import TraderId
from nautilus_trader.adapters.binance import BINANCE, BinanceDataClientConfig, BinanceAccountType
from nautilus_trader.adapters.binance import BinanceLiveDataClientFactory
from nautilus_trader.model.identifiers import InstrumentId


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_binance_connection():
    """测试Binance连接"""
    
    logger.info("🚀 Testing Binance connection...")
    
    # 创建节点配置
    config = TradingNodeConfig(
        trader_id=TraderId("BINANCE-TEST"),
        data_clients={
            "BINANCE-SPOT": BinanceDataClientConfig(
                api_key="2s39JuJ3j4egAQObcRhIwAKKqtlpb4hl6UcPlBFjajjd27ilMG3Ye3wZdjcxpoCb",
                api_secret="2BaaJLjZSgJjFIe4ALNcbhokksRYx9JvrdAQLElVW9Hh9TUw8iv006riUait9xPZ",
                account_type=BinanceAccountType.SPOT,
                base_url_http="https://testnet.binance.vision",
                base_url_ws="wss://testnet.binance.vision",
                testnet=True,
                us=False,
            ),
        },
        exec_clients={},
    )
    
    # 创建交易节点
    node = TradingNode(config=config)
    
    # 添加Binance数据客户端工厂
    node.add_data_client_factory(BINANCE, BinanceLiveDataClientFactory)
    
    try:
        # 构建节点
        logger.info("📦 Building node...")
        node.build()
        
        # 启动节点
        logger.info("🔌 Starting node...")
        run_task = asyncio.create_task(node.run_async())
        
        # 等待节点启动
        await asyncio.sleep(3)
        
        if node.is_running:
            logger.info("✅ Node is running!")
            
            # 获取数据引擎
            data_engine = node.kernel.data_engine
            logger.info(f"📊 Data engine: {data_engine}")
            
            # 获取数据客户端
            data_clients = data_engine.registered_clients
            logger.info(f"🔗 Registered data clients: {len(data_clients)}")
            
            for client in data_clients:
                logger.info(f"  - {client}")
            
            # 检查是否有可用的交易对
            cache = node.kernel.cache
            instruments = cache.instruments()
            logger.info(f"🎯 Available instruments: {len(instruments)}")
            
            if instruments:
                for instrument in list(instruments)[:5]:  # 显示前5个
                    logger.info(f"  - {instrument.id}: {instrument}")
            else:
                logger.warning("⚠️ No instruments found in cache")
                
                # 尝试手动请求一些交易对信息
                logger.info("🔍 Trying to request instrument info...")
                
                # 这里可以添加手动请求交易对信息的代码
                # 但需要直接访问Binance客户端
            
            # 运行一段时间
            logger.info("⏰ Running for 10 seconds...")
            await asyncio.sleep(10)
            
        else:
            logger.error("❌ Node failed to start")
            
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 停止节点
        if node.is_running:
            logger.info("🛑 Stopping node...")
            node.stop()
            
            # 等待运行任务完成
            if 'run_task' in locals() and not run_task.done():
                run_task.cancel()
                try:
                    await run_task
                except asyncio.CancelledError:
                    pass
            
            node.dispose()
            logger.info("✅ Node stopped")


if __name__ == "__main__":
    asyncio.run(test_binance_connection())
