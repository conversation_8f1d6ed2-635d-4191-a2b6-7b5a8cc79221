#!/usr/bin/env python3
"""
简单的Binance数据订阅 - 完全参照官方示例
"""

from decimal import Decimal

from nautilus_trader.adapters.binance import BINANCE
from nautilus_trader.adapters.binance import BinanceAccountType
from nautilus_trader.adapters.binance import BinanceDataClientConfig
from nautilus_trader.adapters.binance import BinanceLiveDataClientFactory
from nautilus_trader.config import InstrumentProviderConfig
from nautilus_trader.config import LoggingConfig
from nautilus_trader.config import TradingNodeConfig
from nautilus_trader.live.node import TradingNode
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.identifiers import TraderId
from nautilus_trader.model.identifiers import StrategyId
from nautilus_trader.trading.strategy import Strategy
from nautilus_trader.model.data import QuoteTick, TradeTick


class DataSubscriber(Strategy):
    """
    简单的数据订阅策略
    """

    def __init__(self, config=None):
        super().__init__(config)

    def on_start(self) -> None:
        """
        策略启动时的操作
        """
        self.log.info("🚀 Starting data subscription...")
        
        # 订阅BTCUSDT的数据
        btc_instrument = InstrumentId.from_str("BTCUSDT.BINANCE")
        self.subscribe_quote_ticks(btc_instrument)
        self.subscribe_trade_ticks(btc_instrument)
        self.log.info(f"📡 Subscribed to {btc_instrument}")
        
        # 订阅ETHUSDT的数据
        eth_instrument = InstrumentId.from_str("ETHUSDT.BINANCE")
        self.subscribe_quote_ticks(eth_instrument)
        self.subscribe_trade_ticks(eth_instrument)
        self.log.info(f"📡 Subscribed to {eth_instrument}")

    def on_quote_tick(self, tick: QuoteTick) -> None:
        """
        处理报价数据
        """
        self.log.info(f"📊 Quote: {tick.instrument_id} - Bid: {tick.bid_price} Ask: {tick.ask_price}")

    def on_trade_tick(self, tick: TradeTick) -> None:
        """
        处理交易数据
        """
        self.log.info(f"💰 Trade: {tick.instrument_id} - Price: {tick.price} Size: {tick.size}")


# 配置交易节点
config_node = TradingNodeConfig(
    trader_id=TraderId("BINANCE-DATA-SUB"),
    logging=LoggingConfig(log_level="INFO"),
    data_clients={
        BINANCE: BinanceDataClientConfig(
            api_key="2s39JuJ3j4egAQObcRhIwAKKqtlpb4hl6UcPlBFjajjd27ilMG3Ye3wZdjcxpoCb",
            api_secret="2BaaJLjZSgJjFIe4ALNcbhokksRYx9JvrdAQLElVW9Hh9TUw8iv006riUait9xPZ",
            account_type=BinanceAccountType.SPOT,
            base_url_http="https://testnet.binance.vision",
            base_url_ws="wss://testnet.binance.vision",
            us=False,
            testnet=True,
            instrument_provider=InstrumentProviderConfig(load_all=True),
        ),
    },
    exec_clients={},  # 不需要执行客户端，只订阅数据
    timeout_connection=30.0,
    timeout_reconciliation=10.0,
    timeout_portfolio=10.0,
    timeout_disconnection=10.0,
    timeout_post_stop=5.0,
)

# 创建节点
node = TradingNode(config=config_node)

# 创建策略
strategy = DataSubscriber()

# 添加策略
node.trader.add_strategy(strategy)

# 注册客户端工厂
node.add_data_client_factory(BINANCE, BinanceLiveDataClientFactory)
node.build()


# 运行节点
if __name__ == "__main__":
    try:
        node.run()
    finally:
        node.dispose()
