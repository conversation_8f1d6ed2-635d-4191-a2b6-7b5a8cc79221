"""
Binance数据订阅测试 - 专注于实时数据订阅
"""

import asyncio
import logging
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
import sys
sys.path.insert(0, str(project_root / "src"))

from nautilus_trader.config import TradingNodeConfig
from nautilus_trader.live.node import TradingNode
from nautilus_trader.model.identifiers import TraderId, InstrumentId
from nautilus_trader.adapters.binance import BINANCE, BinanceDataClientConfig, BinanceAccountType
from nautilus_trader.adapters.binance import BinanceLiveDataClientFactory
from nautilus_trader.model.data import QuoteTick, TradeTick


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BinanceDataSubscriber:
    """Binance数据订阅器"""
    
    def __init__(self):
        self.node = None
        self.data_count = 0
        self.shutdown_event = asyncio.Event()
        
    def create_node_config(self) -> TradingNodeConfig:
        """创建节点配置"""
        return TradingNodeConfig(
            trader_id=TraderId("BINANCE-DATA-SUB"),
            data_clients={
                "BINANCE-SPOT": BinanceDataClientConfig(
                    api_key="2s39JuJ3j4egAQObcRhIwAKKqtlpb4hl6UcPlBFjajjd27ilMG3Ye3wZdjcxpoCb",
                    api_secret="2BaaJLjZSgJjFIe4ALNcbhokksRYx9JvrdAQLElVW9Hh9TUw8iv006riUait9xPZ",
                    account_type=BinanceAccountType.SPOT,
                    base_url_http="https://testnet.binance.vision",
                    base_url_ws="wss://testnet.binance.vision",
                    testnet=True,
                    us=False,
                ),
            },
            exec_clients={},
        )
    
    def on_quote_tick(self, tick: QuoteTick):
        """处理报价数据"""
        self.data_count += 1
        if self.data_count % 10 == 0:  # 每10个tick显示一次
            logger.info(f"📊 Quote #{self.data_count}: {tick.instrument_id} - "
                       f"Bid: {tick.bid_price} Ask: {tick.ask_price}")
    
    def on_trade_tick(self, tick: TradeTick):
        """处理交易数据"""
        self.data_count += 1
        if self.data_count % 5 == 0:  # 每5个tick显示一次
            logger.info(f"💰 Trade #{self.data_count}: {tick.instrument_id} - "
                       f"Price: {tick.price} Size: {tick.size} Side: {tick.aggressor_side}")
    
    async def start_subscription(self):
        """开始数据订阅"""
        try:
            logger.info("🚀 Starting Binance data subscription...")
            
            # 创建节点配置
            config = self.create_node_config()
            
            # 创建交易节点
            self.node = TradingNode(config=config)
            
            # 添加Binance数据客户端工厂
            self.node.add_data_client_factory(BINANCE, BinanceLiveDataClientFactory)
            
            # 构建节点
            self.node.build()
            
            # 在后台启动节点
            run_task = asyncio.create_task(self.node.run_async())
            
            # 等待节点启动
            await asyncio.sleep(3)
            
            if self.node.is_running:
                logger.info("✅ Node started successfully!")
                
                # 手动创建交易对并订阅数据
                await self.subscribe_to_data()
                
                # 等待关闭信号
                logger.info("🔄 Receiving data... Press Ctrl+C to stop")
                await self.shutdown_event.wait()
                
            else:
                logger.error("❌ Failed to start node")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error in subscription: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        finally:
            # 停止节点
            if self.node and self.node.is_running:
                logger.info("🛑 Stopping node...")
                self.node.stop()
                
                # 等待运行任务完成
                if 'run_task' in locals() and not run_task.done():
                    run_task.cancel()
                    try:
                        await run_task
                    except asyncio.CancelledError:
                        pass
                
                self.node.dispose()
                logger.info("✅ Node stopped")
        
        return True
    
    async def subscribe_to_data(self):
        """订阅数据"""
        logger.info("📡 Setting up data subscriptions...")
        
        # 手动创建交易对ID（不依赖缓存中的交易对）
        symbols = [
            "BTCUSDT.BINANCE",
            "ETHUSDT.BINANCE",
            "BNBUSDT.BINANCE"
        ]
        
        # 注册数据处理器
        msgbus = self.node.kernel.msgbus
        
        # 订阅报价数据
        msgbus.subscribe(
            topic="data.quotes.*",
            handler=self.on_quote_tick
        )
        
        # 订阅交易数据
        msgbus.subscribe(
            topic="data.trades.*",
            handler=self.on_trade_tick
        )
        
        # 通过数据客户端订阅数据
        data_clients = self.node.kernel.data_engine.registered_clients
        binance_client = None

        for client_id in data_clients:
            if "BINANCE" in str(client_id):
                # 获取实际的客户端对象
                binance_client = self.node.kernel.data_engine.get_client(client_id)
                break

        if not binance_client:
            logger.error("❌ Binance client not found")
            return

        logger.info(f"📡 Found Binance client: {binance_client}")

        for symbol in symbols:
            try:
                instrument_id = InstrumentId.from_str(symbol)
                logger.info(f"📊 Subscribing to {symbol}...")

                # 通过客户端订阅数据
                binance_client.subscribe_quote_ticks(instrument_id)
                binance_client.subscribe_trade_ticks(instrument_id)

                logger.info(f"✅ Subscribed to {symbol}")
                await asyncio.sleep(0.5)  # 避免订阅过快

            except Exception as e:
                logger.error(f"❌ Failed to subscribe to {symbol}: {e}")
                import traceback
                traceback.print_exc()
        
        logger.info("📡 All subscriptions completed!")
    
    async def show_statistics(self):
        """显示统计信息"""
        while not self.shutdown_event.is_set():
            await asyncio.sleep(10)  # 每10秒显示一次统计
            
            if self.node and self.node.is_running:
                logger.info(f"📈 Total data received: {self.data_count} ticks")
                
                # 显示缓存中的数据
                cache = self.node.kernel.cache
                instruments = cache.instruments()
                
                if instruments:
                    logger.info(f"📊 Cached instruments: {len(instruments)}")
                    for instrument in list(instruments)[:3]:  # 只显示前3个
                        quotes = cache.quote_ticks(instrument.id)
                        trades = cache.trade_ticks(instrument.id)
                        logger.info(f"  {instrument.id}: {len(quotes)} quotes, {len(trades)} trades")
                else:
                    logger.info("📊 No instruments in cache yet")


async def main():
    """主函数"""
    subscriber = BinanceDataSubscriber()
    
    # 设置信号处理器
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}, stopping...")
        subscriber.shutdown_event.set()
    
    import signal
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 启动统计任务
    stats_task = asyncio.create_task(subscriber.show_statistics())
    
    try:
        # 启动订阅
        success = await subscriber.start_subscription()
        
        if not success:
            logger.error("Failed to start subscription")
            return 1
            
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return 1
    finally:
        # 取消统计任务
        stats_task.cancel()
        try:
            await stats_task
        except asyncio.CancelledError:
            pass
    
    return 0


if __name__ == "__main__":
    # 运行主函数
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
