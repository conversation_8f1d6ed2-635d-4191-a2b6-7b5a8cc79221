"""
Integration tests for Binance API connections.
"""

import pytest
import asyncio
from funding_rate_arbitrage.binance_client import BinanceAPITester
from funding_rate_arbitrage.config import AppConfig


@pytest.fixture
def app_config():
    """Create app configuration for testing."""
    return AppConfig.from_env()


@pytest.fixture
def api_tester(app_config):
    """Create BinanceAPITester instance."""
    return BinanceAPITester(app_config)


def test_config_validation(app_config):
    """Test that API configuration is valid."""
    assert app_config.validate_api_keys(), "API keys should be valid"
    assert app_config.binance.testnet is True, "Should be using testnet"


@pytest.mark.asyncio
async def test_spot_connection(api_tester):
    """Test Binance spot API connection."""
    result = await api_tester.test_spot_connection()
    
    assert result["status"] == "success", f"Spot connection failed: {result.get('message')}"
    assert result["account_type"] == "SPOT"
    assert result["testnet"] is True


@pytest.mark.asyncio
async def test_futures_connection(api_tester):
    """Test Binance futures API connection."""
    result = await api_tester.test_futures_connection()
    
    assert result["status"] == "success", f"Futures connection failed: {result.get('message')}"
    assert result["account_type"] == "USDT_FUTURE"
    assert result["testnet"] is True


@pytest.mark.asyncio
async def test_all_connections(api_tester):
    """Test all Binance API connections."""
    result = await api_tester.test_all_connections()
    
    assert result["overall_status"] == "success", "All connections should succeed"
    assert result["summary"]["successful"] == 2, "Both spot and futures should succeed"
    assert result["summary"]["failed"] == 0, "No connections should fail"
    
    # Check individual results
    assert result["results"]["spot"]["status"] == "success"
    assert result["results"]["futures"]["status"] == "success"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
