"""
Tests for configuration management.
"""

import os
import pytest
from funding_rate_arbitrage.config import AppConfig, BinanceConfig, RiskConfig, LoggingConfig


def test_binance_config_from_env():
    """Test BinanceConfig creation from environment variables."""
    # Set test environment variables
    os.environ["BINANCE_API_KEY"] = "test_api_key"
    os.environ["BINANCE_API_SECRET"] = "test_api_secret"
    os.environ["BINANCE_FUTURES_API_KEY"] = "test_futures_api_key"
    os.environ["BINANCE_FUTURES_API_SECRET"] = "test_futures_api_secret"
    os.environ["BINANCE_TESTNET"] = "true"
    
    config = BinanceConfig.from_env()
    
    assert config.api_key == "test_api_key"
    assert config.api_secret == "test_api_secret"
    assert config.futures_api_key == "test_futures_api_key"
    assert config.futures_api_secret == "test_futures_api_secret"
    assert config.testnet is True


def test_risk_config_from_env():
    """Test RiskConfig creation from environment variables."""
    os.environ["MAX_POSITION_SIZE"] = "2000"
    os.environ["MAX_DAILY_LOSS"] = "200"
    os.environ["FUNDING_RATE_THRESHOLD"] = "0.02"
    
    config = RiskConfig.from_env()
    
    assert config.max_position_size == 2000.0
    assert config.max_daily_loss == 200.0
    assert config.funding_rate_threshold == 0.02


def test_logging_config_from_env():
    """Test LoggingConfig creation from environment variables."""
    os.environ["LOG_LEVEL"] = "DEBUG"
    os.environ["LOG_TO_FILE"] = "false"
    
    config = LoggingConfig.from_env()
    
    assert config.level == "DEBUG"
    assert config.to_file is False


def test_app_config_validation():
    """Test AppConfig API key validation."""
    # Set valid API keys
    os.environ["BINANCE_API_KEY"] = "valid_key"
    os.environ["BINANCE_API_SECRET"] = "valid_secret"
    os.environ["BINANCE_FUTURES_API_KEY"] = "valid_futures_key"
    os.environ["BINANCE_FUTURES_API_SECRET"] = "valid_futures_secret"
    
    config = AppConfig.from_env()
    assert config.validate_api_keys() is True
    
    # Test with empty API key
    os.environ["BINANCE_API_KEY"] = ""
    config = AppConfig.from_env()
    assert config.validate_api_keys() is False


if __name__ == "__main__":
    pytest.main([__file__])
