"""
系统编排器 - 管理多节点交易系统的启动、停止和协调
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

from nautilus_trader.config import TradingNodeConfig
from nautilus_trader.live.node import TradingNode
from nautilus_trader.model.identifiers import TraderId

from .data_node import create_data_node_config
from .strategy_node import create_strategy_node_config  
from .execution_node import create_execution_node_config
from .risk_node import create_risk_node_config


class NodeType(Enum):
    """节点类型"""
    DATA = "data"
    STRATEGY = "strategy"
    EXECUTION = "execution"
    RISK = "risk"


@dataclass
class NodeInfo:
    """节点信息"""
    name: str
    node_type: NodeType
    config: TradingNodeConfig
    node: Optional[TradingNode] = None
    run_task: Optional[asyncio.Task] = None
    status: str = "stopped"  # stopped, starting, running, stopping, error


class SystemOrchestrator:
    """系统编排器"""
    
    def __init__(self, system_config: Dict[str, Any]):
        self.system_config = system_config
        self.nodes: Dict[str, NodeInfo] = {}
        self.startup_order = [NodeType.DATA, NodeType.RISK, NodeType.EXECUTION, NodeType.STRATEGY]
        self.shutdown_order = list(reversed(self.startup_order))
        self.logger = logging.getLogger(__name__)
        
    def register_data_nodes(self, configs: List[Dict[str, Any]]):
        """注册数据节点"""
        for config in configs:
            node_config = create_data_node_config(config)
            node_info = NodeInfo(
                name=config["name"],
                node_type=NodeType.DATA,
                config=node_config
            )
            self.nodes[config["name"]] = node_info
            self.logger.info(f"Registered data node: {config['name']}")
    
    def register_strategy_nodes(self, configs: List[Dict[str, Any]]):
        """注册策略节点"""
        for config in configs:
            node_config = create_strategy_node_config(config)
            node_info = NodeInfo(
                name=config["name"],
                node_type=NodeType.STRATEGY,
                config=node_config
            )
            self.nodes[config["name"]] = node_info
            self.logger.info(f"Registered strategy node: {config['name']}")
    
    def register_execution_nodes(self, configs: List[Dict[str, Any]]):
        """注册执行节点"""
        for config in configs:
            node_config = create_execution_node_config(config)
            node_info = NodeInfo(
                name=config["name"],
                node_type=NodeType.EXECUTION,
                config=node_config
            )
            self.nodes[config["name"]] = node_info
            self.logger.info(f"Registered execution node: {config['name']}")
    
    def register_risk_nodes(self, configs: List[Dict[str, Any]]):
        """注册风控节点"""
        for config in configs:
            node_config = create_risk_node_config(config)
            node_info = NodeInfo(
                name=config["name"],
                node_type=NodeType.RISK,
                config=node_config
            )
            self.nodes[config["name"]] = node_info
            self.logger.info(f"Registered risk node: {config['name']}")
    
    def get_nodes_by_type(self, node_type: NodeType) -> List[NodeInfo]:
        """按类型获取节点"""
        return [node for node in self.nodes.values() if node.node_type == node_type]
    
    async def start_node(self, node_info: NodeInfo) -> bool:
        """启动单个节点"""
        try:
            self.logger.info(f"Starting node: {node_info.name}")
            node_info.status = "starting"

            # 创建TradingNode
            node_info.node = TradingNode(config=node_info.config)

            # 根据节点类型添加相应的工厂
            await self._setup_node_factories(node_info)

            # 构建节点
            node_info.node.build()

            # 在后台启动节点
            node_info.run_task = asyncio.create_task(node_info.node.run_async())

            # 等待一小段时间确保节点启动
            await asyncio.sleep(1)

            # 检查节点是否正在运行
            if node_info.node.is_running:
                node_info.status = "running"
                self.logger.info(f"✅ Node started successfully: {node_info.name}")
                return True
            else:
                node_info.status = "error"
                self.logger.error(f"❌ Node failed to start: {node_info.name}")
                return False

        except Exception as e:
            node_info.status = "error"
            self.logger.error(f"❌ Failed to start node {node_info.name}: {e}")
            return False
    
    async def stop_node(self, node_info: NodeInfo) -> bool:
        """停止单个节点"""
        try:
            if node_info.node and node_info.status == "running":
                self.logger.info(f"Stopping node: {node_info.name}")
                node_info.status = "stopping"

                # 停止节点
                node_info.node.stop()

                # 取消运行任务
                if node_info.run_task and not node_info.run_task.done():
                    node_info.run_task.cancel()
                    try:
                        await node_info.run_task
                    except asyncio.CancelledError:
                        pass

                # 清理资源
                node_info.node.dispose()
                node_info.node = None
                node_info.run_task = None

                node_info.status = "stopped"
                self.logger.info(f"✅ Node stopped successfully: {node_info.name}")
            return True

        except Exception as e:
            node_info.status = "error"
            self.logger.error(f"❌ Failed to stop node {node_info.name}: {e}")
            return False
    
    async def _setup_node_factories(self, node_info: NodeInfo):
        """为节点设置相应的工厂"""
        # 这里需要根据节点类型和配置添加相应的工厂
        # 例如：Binance工厂、OKX工厂、自定义适配器工厂等

        if node_info.node_type == NodeType.DATA:
            # 添加数据客户端工厂
            from nautilus_trader.adapters.binance import BinanceLiveDataClientFactory
            from nautilus_trader.adapters.binance import BINANCE
            node_info.node.add_data_client_factory(BINANCE, BinanceLiveDataClientFactory)

            # TODO: 添加其他交易所和数据源的工厂
            # node_info.node.add_data_client_factory(OKX, OKXLiveDataClientFactory)
            # node_info.node.add_data_client_factory(NEWS, NewsDataClientFactory)

        elif node_info.node_type == NodeType.EXECUTION:
            # 添加执行客户端工厂
            from nautilus_trader.adapters.binance import BinanceLiveExecClientFactory
            from nautilus_trader.adapters.binance import BINANCE
            node_info.node.add_exec_client_factory(BINANCE, BinanceLiveExecClientFactory)

            # TODO: 添加其他交易所的执行工厂
            # node_info.node.add_exec_client_factory(OKX, OKXLiveExecClientFactory)
    
    async def start_system(self) -> bool:
        """启动整个系统"""
        self.logger.info("🚀 Starting multi-strategy trading system...")
        
        success_count = 0
        total_count = 0
        
        # 按启动顺序启动节点
        for node_type in self.startup_order:
            nodes = self.get_nodes_by_type(node_type)
            
            if not nodes:
                continue
                
            self.logger.info(f"Starting {node_type.value} nodes...")
            
            # 并行启动同类型的节点
            tasks = [self.start_node(node) for node in nodes]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for node, result in zip(nodes, results):
                total_count += 1
                if isinstance(result, bool) and result:
                    success_count += 1
                else:
                    self.logger.error(f"Failed to start {node.name}: {result}")
            
            # 等待一段时间让节点稳定
            await asyncio.sleep(2)
        
        success_rate = success_count / total_count if total_count > 0 else 0
        
        if success_rate == 1.0:
            self.logger.info("🎉 All nodes started successfully!")
            return True
        elif success_rate > 0.5:
            self.logger.warning(f"⚠️ Partial system startup: {success_count}/{total_count} nodes started")
            return True
        else:
            self.logger.error(f"❌ System startup failed: {success_count}/{total_count} nodes started")
            return False
    
    async def stop_system(self) -> bool:
        """停止整个系统"""
        self.logger.info("🛑 Stopping multi-strategy trading system...")
        
        success_count = 0
        total_count = 0
        
        # 按停止顺序停止节点
        for node_type in self.shutdown_order:
            nodes = self.get_nodes_by_type(node_type)
            
            if not nodes:
                continue
                
            self.logger.info(f"Stopping {node_type.value} nodes...")
            
            # 并行停止同类型的节点
            tasks = [self.stop_node(node) for node in nodes]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for node, result in zip(nodes, results):
                total_count += 1
                if isinstance(result, bool) and result:
                    success_count += 1
                else:
                    self.logger.error(f"Failed to stop {node.name}: {result}")
            
            # 等待一段时间让节点完全停止
            await asyncio.sleep(1)
        
        success_rate = success_count / total_count if total_count > 0 else 1.0
        
        if success_rate >= 0.8:  # 允许一些节点停止失败
            self.logger.info("✅ System stopped successfully!")
            return True
        else:
            self.logger.error(f"❌ System shutdown had issues: {success_count}/{total_count} nodes stopped")
            return False
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        status_by_type = {}
        for node_type in NodeType:
            nodes = self.get_nodes_by_type(node_type)
            status_by_type[node_type.value] = {
                "total": len(nodes),
                "running": len([n for n in nodes if n.status == "running"]),
                "stopped": len([n for n in nodes if n.status == "stopped"]),
                "error": len([n for n in nodes if n.status == "error"]),
                "nodes": {n.name: n.status for n in nodes}
            }
        
        total_nodes = len(self.nodes)
        running_nodes = len([n for n in self.nodes.values() if n.status == "running"])
        
        return {
            "total_nodes": total_nodes,
            "running_nodes": running_nodes,
            "system_health": running_nodes / total_nodes if total_nodes > 0 else 0,
            "nodes_by_type": status_by_type
        }
