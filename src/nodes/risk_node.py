"""
风控节点配置 - 专门负责风险管理
"""

from typing import Dict, Any, List
from nautilus_trader.config import TradingNodeConfig, LoggingConfig, ActorConfig
from nautilus_trader.model.identifiers import TraderId


def create_risk_node_config(config: Dict[str, Any]) -> TradingNodeConfig:
    """创建风控节点配置"""
    
    # 基础配置
    node_name = config.get("name", "risk-node")
    trader_id = TraderId(f"{node_name.upper()}")
    
    # Actor配置列表
    actors = []
    
    # 仓位监控Actor
    if config.get("position_monitor", {}).get("enabled", False):
        actors.append(
            create_position_monitor_config(
                config["position_monitor"]
            )
        )
    
    # 敞口管理Actor
    if config.get("exposure_manager", {}).get("enabled", False):
        actors.append(
            create_exposure_manager_config(
                config["exposure_manager"]
            )
        )
    
    # 回撤监控Actor
    if config.get("drawdown_monitor", {}).get("enabled", False):
        actors.append(
            create_drawdown_monitor_config(
                config["drawdown_monitor"]
            )
        )
    
    # 流动性管理Actor
    if config.get("liquidity_manager", {}).get("enabled", False):
        actors.append(
            create_liquidity_manager_config(
                config["liquidity_manager"]
            )
        )
    
    # 创建节点配置
    return TradingNodeConfig(
        trader_id=trader_id,
        logging=LoggingConfig(
            log_level=config.get("log_level", "INFO"),
            log_directory=config.get("log_directory", "logs"),
            log_file_name=config.get("log_file_name", "risk_node.log"),
        ),
        data_clients={},  # 风控节点从消息总线获取数据
        exec_clients={},  # 风控节点不直接执行订单
        actors=actors,
        timeout_connection=config.get("timeout_connection", 10.0),
        timeout_reconciliation=config.get("timeout_reconciliation", 5.0),
        timeout_portfolio=config.get("timeout_portfolio", 5.0),
        timeout_disconnection=config.get("timeout_disconnection", 5.0),
    )


def create_position_monitor_config(config: Dict[str, Any]) -> ActorConfig:
    """创建仓位监控Actor配置"""
    return ActorConfig(
        actor_path="actors.risk_managers.position_monitor:PositionMonitor",
        config_path="actors.risk_managers.position_monitor:PositionMonitorConfig",
        config={
            "max_position_size": config.get("max_position_size", 10000.0),
            "max_positions_per_symbol": config.get("max_positions_per_symbol", 5),
            "position_timeout": config.get("position_timeout", 3600),  # 1 hour
            "alert_threshold": config.get("alert_threshold", 0.8),  # 80% of max
        }
    )


def create_exposure_manager_config(config: Dict[str, Any]) -> ActorConfig:
    """创建敞口管理Actor配置"""
    return ActorConfig(
        actor_path="actors.risk_managers.exposure_manager:ExposureManager",
        config_path="actors.risk_managers.exposure_manager:ExposureManagerConfig",
        config={
            "max_total_exposure": config.get("max_total_exposure", 100000.0),
            "max_sector_exposure": config.get("max_sector_exposure", 50000.0),
            "max_single_asset_exposure": config.get("max_single_asset_exposure", 20000.0),
            "correlation_threshold": config.get("correlation_threshold", 0.7),
            "rebalance_frequency": config.get("rebalance_frequency", 3600),  # 1 hour
        }
    )


def create_drawdown_monitor_config(config: Dict[str, Any]) -> ActorConfig:
    """创建回撤监控Actor配置"""
    return ActorConfig(
        actor_path="actors.risk_managers.drawdown_monitor:DrawdownMonitor",
        config_path="actors.risk_managers.drawdown_monitor:DrawdownMonitorConfig",
        config={
            "max_daily_drawdown": config.get("max_daily_drawdown", 0.05),  # 5%
            "max_total_drawdown": config.get("max_total_drawdown", 0.20),  # 20%
            "stop_trading_threshold": config.get("stop_trading_threshold", 0.15),  # 15%
            "recovery_threshold": config.get("recovery_threshold", 0.10),  # 10%
            "lookback_period": config.get("lookback_period", 30),  # 30 days
        }
    )


def create_liquidity_manager_config(config: Dict[str, Any]) -> ActorConfig:
    """创建流动性管理Actor配置"""
    return ActorConfig(
        actor_path="actors.risk_managers.liquidity_manager:LiquidityManager",
        config_path="actors.risk_managers.liquidity_manager:LiquidityManagerConfig",
        config={
            "min_liquidity_threshold": config.get("min_liquidity_threshold", 1000000.0),
            "max_market_impact": config.get("max_market_impact", 0.001),  # 0.1%
            "order_size_limit": config.get("order_size_limit", 0.05),  # 5% of volume
            "slippage_tolerance": config.get("slippage_tolerance", 0.002),  # 0.2%
            "liquidity_check_interval": config.get("liquidity_check_interval", 300),  # 5 minutes
        }
    )


# 示例风控节点配置
EXAMPLE_RISK_NODE_CONFIG = {
    "name": "main-risk-node",
    "log_level": "INFO",
    "log_to_file": True,
    "position_monitor": {
        "enabled": True,
        "max_position_size": 10000.0,
        "max_positions_per_symbol": 5,
        "position_timeout": 3600,
        "alert_threshold": 0.8
    },
    "exposure_manager": {
        "enabled": True,
        "max_total_exposure": 100000.0,
        "max_sector_exposure": 50000.0,
        "max_single_asset_exposure": 20000.0,
        "correlation_threshold": 0.7,
        "rebalance_frequency": 3600
    },
    "drawdown_monitor": {
        "enabled": True,
        "max_daily_drawdown": 0.05,
        "max_total_drawdown": 0.20,
        "stop_trading_threshold": 0.15,
        "recovery_threshold": 0.10,
        "lookback_period": 30
    },
    "liquidity_manager": {
        "enabled": True,
        "min_liquidity_threshold": 1000000.0,
        "max_market_impact": 0.001,
        "order_size_limit": 0.05,
        "slippage_tolerance": 0.002,
        "liquidity_check_interval": 300
    }
}
