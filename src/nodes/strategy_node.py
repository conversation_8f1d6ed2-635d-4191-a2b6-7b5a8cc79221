"""
策略节点配置 - 专门负责策略执行
"""

from typing import Dict, Any, List
from nautilus_trader.config import TradingNodeConfig, LoggingConfig, StrategyConfig
from nautilus_trader.model.identifiers import TraderId


def create_strategy_node_config(config: Dict[str, Any]) -> TradingNodeConfig:
    """创建策略节点配置"""
    
    # 基础配置
    node_name = config.get("name", "strategy-node")
    trader_id = TraderId(f"{node_name.upper()}")
    
    # 策略配置列表
    strategies = []
    
    # 套利策略配置
    if "arbitrage" in config.get("strategies", {}):
        arb_config = config["strategies"]["arbitrage"]
        
        # 跨所套利
        if arb_config.get("cross_exchange", {}).get("enabled", False):
            strategies.append(
                create_cross_exchange_arbitrage_config(
                    arb_config["cross_exchange"]
                )
            )
        
        # 期现套利
        if arb_config.get("spot_futures", {}).get("enabled", False):
            strategies.append(
                create_spot_futures_arbitrage_config(
                    arb_config["spot_futures"]
                )
            )
        
        # CEX-DEX套利
        if arb_config.get("cex_dex", {}).get("enabled", False):
            strategies.append(
                create_cex_dex_arbitrage_config(
                    arb_config["cex_dex"]
                )
            )
        
        # 资金费率套利
        if arb_config.get("funding_rate", {}).get("enabled", False):
            strategies.append(
                create_funding_rate_arbitrage_config(
                    arb_config["funding_rate"]
                )
            )
    
    # 做市策略配置
    if "market_making" in config.get("strategies", {}):
        mm_config = config["strategies"]["market_making"]
        
        # 网格策略
        if mm_config.get("grid", {}).get("enabled", False):
            strategies.append(
                create_grid_strategy_config(
                    mm_config["grid"]
                )
            )
        
        # 流动性提供策略
        if mm_config.get("liquidity", {}).get("enabled", False):
            strategies.append(
                create_liquidity_strategy_config(
                    mm_config["liquidity"]
                )
            )
    
    # 趋势策略配置
    if "trend" in config.get("strategies", {}):
        trend_config = config["strategies"]["trend"]
        
        # 动量策略
        if trend_config.get("momentum", {}).get("enabled", False):
            strategies.append(
                create_momentum_strategy_config(
                    trend_config["momentum"]
                )
            )
        
        # 突破策略
        if trend_config.get("breakout", {}).get("enabled", False):
            strategies.append(
                create_breakout_strategy_config(
                    trend_config["breakout"]
                )
            )
    
    # 新闻驱动策略配置
    if "news_driven" in config.get("strategies", {}):
        news_config = config["strategies"]["news_driven"]
        
        # 情感分析策略
        if news_config.get("sentiment", {}).get("enabled", False):
            strategies.append(
                create_sentiment_strategy_config(
                    news_config["sentiment"]
                )
            )
        
        # 事件驱动策略
        if news_config.get("event", {}).get("enabled", False):
            strategies.append(
                create_event_strategy_config(
                    news_config["event"]
                )
            )
    
    # 创建节点配置
    # 注意：策略将在节点启动后通过add_strategy方法添加
    return TradingNodeConfig(
        trader_id=trader_id,
        data_clients={},  # 策略节点从共享数据源获取数据
        exec_clients={},  # 策略节点通过消息总线发送订单
    )


def create_cross_exchange_arbitrage_config(config: Dict[str, Any]) -> StrategyConfig:
    """创建跨所套利策略配置"""
    return StrategyConfig(
        strategy_path="strategies.arbitrage.cross_exchange_arb:CrossExchangeArbitrageStrategy",
        config_path="strategies.arbitrage.cross_exchange_arb:CrossExchangeArbitrageConfig",
        config={
            "instrument_id": config.get("instrument_id", "BTCUSDT"),
            "exchange_a": config.get("exchange_a", "BINANCE"),
            "exchange_b": config.get("exchange_b", "OKX"),
            "min_profit_threshold": config.get("min_profit_threshold", 0.001),  # 0.1%
            "max_position_size": config.get("max_position_size", 1000.0),
            "order_size": config.get("order_size", 100.0),
        }
    )


def create_spot_futures_arbitrage_config(config: Dict[str, Any]) -> StrategyConfig:
    """创建期现套利策略配置"""
    return StrategyConfig(
        strategy_path="strategies.arbitrage.spot_futures_arb:SpotFuturesArbitrageStrategy",
        config_path="strategies.arbitrage.spot_futures_arb:SpotFuturesArbitrageConfig",
        config={
            "instrument_id": config.get("instrument_id", "BTCUSDT"),
            "exchange": config.get("exchange", "BINANCE"),
            "min_spread_threshold": config.get("min_spread_threshold", 0.002),  # 0.2%
            "funding_rate_threshold": config.get("funding_rate_threshold", 0.01),  # 1%
            "max_position_size": config.get("max_position_size", 1000.0),
            "order_size": config.get("order_size", 100.0),
        }
    )


def create_cex_dex_arbitrage_config(config: Dict[str, Any]) -> StrategyConfig:
    """创建CEX-DEX套利策略配置"""
    return StrategyConfig(
        strategy_path="strategies.arbitrage.cex_dex_arb:CexDexArbitrageStrategy",
        config_path="strategies.arbitrage.cex_dex_arb:CexDexArbitrageConfig",
        config={
            "token_address": config.get("token_address", "0x..."),
            "cex_exchange": config.get("cex_exchange", "BINANCE"),
            "dex_exchange": config.get("dex_exchange", "UNISWAP"),
            "min_profit_threshold": config.get("min_profit_threshold", 0.005),  # 0.5%
            "max_position_size": config.get("max_position_size", 1000.0),
            "gas_price_limit": config.get("gas_price_limit", 50),  # Gwei
        }
    )


def create_funding_rate_arbitrage_config(config: Dict[str, Any]) -> StrategyConfig:
    """创建资金费率套利策略配置"""
    return StrategyConfig(
        strategy_path="strategies.arbitrage.funding_rate_arb:FundingRateArbitrageStrategy",
        config_path="strategies.arbitrage.funding_rate_arb:FundingRateArbitrageConfig",
        config={
            "instrument_id": config.get("instrument_id", "BTCUSDT"),
            "exchanges": config.get("exchanges", ["BINANCE", "OKX", "BYBIT"]),
            "min_funding_rate": config.get("min_funding_rate", 0.01),  # 1%
            "max_position_size": config.get("max_position_size", 1000.0),
            "rebalance_threshold": config.get("rebalance_threshold", 0.005),  # 0.5%
        }
    )


def create_grid_strategy_config(config: Dict[str, Any]) -> StrategyConfig:
    """创建网格策略配置"""
    return StrategyConfig(
        strategy_path="strategies.market_making.grid_strategy:GridStrategy",
        config_path="strategies.market_making.grid_strategy:GridStrategyConfig",
        config={
            "instrument_id": config.get("instrument_id", "BTCUSDT"),
            "exchange": config.get("exchange", "BINANCE"),
            "grid_levels": config.get("grid_levels", 10),
            "grid_spacing": config.get("grid_spacing", 0.001),  # 0.1%
            "order_size": config.get("order_size", 100.0),
            "price_range": config.get("price_range", [40000, 50000]),
        }
    )


def create_liquidity_strategy_config(config: Dict[str, Any]) -> StrategyConfig:
    """创建流动性策略配置"""
    return StrategyConfig(
        strategy_path="strategies.market_making.liquidity_strategy:LiquidityStrategy",
        config_path="strategies.market_making.liquidity_strategy:LiquidityStrategyConfig",
        config={
            "instrument_id": config.get("instrument_id", "BTCUSDT"),
            "exchange": config.get("exchange", "BINANCE"),
            "spread_bps": config.get("spread_bps", 10),  # 10 basis points
            "order_size": config.get("order_size", 100.0),
            "max_inventory": config.get("max_inventory", 1000.0),
        }
    )


def create_momentum_strategy_config(config: Dict[str, Any]) -> StrategyConfig:
    """创建动量策略配置"""
    return StrategyConfig(
        strategy_path="strategies.trend.momentum_strategy:MomentumStrategy",
        config_path="strategies.trend.momentum_strategy:MomentumStrategyConfig",
        config={
            "instrument_id": config.get("instrument_id", "BTCUSDT"),
            "exchange": config.get("exchange", "BINANCE"),
            "lookback_period": config.get("lookback_period", 20),
            "momentum_threshold": config.get("momentum_threshold", 0.02),  # 2%
            "order_size": config.get("order_size", 100.0),
            "stop_loss": config.get("stop_loss", 0.05),  # 5%
        }
    )


def create_breakout_strategy_config(config: Dict[str, Any]) -> StrategyConfig:
    """创建突破策略配置"""
    return StrategyConfig(
        strategy_path="strategies.trend.breakout_strategy:BreakoutStrategy",
        config_path="strategies.trend.breakout_strategy:BreakoutStrategyConfig",
        config={
            "instrument_id": config.get("instrument_id", "BTCUSDT"),
            "exchange": config.get("exchange", "BINANCE"),
            "breakout_period": config.get("breakout_period", 20),
            "breakout_threshold": config.get("breakout_threshold", 0.01),  # 1%
            "order_size": config.get("order_size", 100.0),
            "stop_loss": config.get("stop_loss", 0.03),  # 3%
        }
    )


def create_sentiment_strategy_config(config: Dict[str, Any]) -> StrategyConfig:
    """创建情感分析策略配置"""
    return StrategyConfig(
        strategy_path="strategies.news_driven.sentiment_strategy:SentimentStrategy",
        config_path="strategies.news_driven.sentiment_strategy:SentimentStrategyConfig",
        config={
            "instrument_id": config.get("instrument_id", "BTCUSDT"),
            "exchange": config.get("exchange", "BINANCE"),
            "sentiment_threshold": config.get("sentiment_threshold", 0.7),
            "news_sources": config.get("news_sources", ["twitter", "reddit"]),
            "order_size": config.get("order_size", 100.0),
            "hold_period": config.get("hold_period", 3600),  # 1 hour
        }
    )


def create_event_strategy_config(config: Dict[str, Any]) -> StrategyConfig:
    """创建事件驱动策略配置"""
    return StrategyConfig(
        strategy_path="strategies.news_driven.event_strategy:EventStrategy",
        config_path="strategies.news_driven.event_strategy:EventStrategyConfig",
        config={
            "instrument_id": config.get("instrument_id", "BTCUSDT"),
            "exchange": config.get("exchange", "BINANCE"),
            "event_types": config.get("event_types", ["earnings", "partnerships", "regulations"]),
            "reaction_time": config.get("reaction_time", 60),  # 60 seconds
            "order_size": config.get("order_size", 100.0),
            "max_exposure": config.get("max_exposure", 1000.0),
        }
    )


# 示例策略节点配置
EXAMPLE_STRATEGY_NODE_CONFIG = {
    "name": "arbitrage-strategy-node",
    "log_level": "INFO",
    "log_to_file": True,
    "strategies": {
        "arbitrage": {
            "cross_exchange": {
                "enabled": True,
                "instrument_id": "BTCUSDT",
                "exchange_a": "BINANCE",
                "exchange_b": "OKX",
                "min_profit_threshold": 0.001,
                "max_position_size": 1000.0,
                "order_size": 100.0
            },
            "spot_futures": {
                "enabled": True,
                "instrument_id": "BTCUSDT",
                "exchange": "BINANCE",
                "min_spread_threshold": 0.002,
                "funding_rate_threshold": 0.01,
                "max_position_size": 1000.0,
                "order_size": 100.0
            },
            "funding_rate": {
                "enabled": False,
                "instrument_id": "BTCUSDT",
                "exchanges": ["BINANCE", "OKX"],
                "min_funding_rate": 0.01,
                "max_position_size": 1000.0
            }
        },
        "market_making": {
            "grid": {
                "enabled": False,
                "instrument_id": "BTCUSDT",
                "exchange": "BINANCE",
                "grid_levels": 10,
                "grid_spacing": 0.001,
                "order_size": 100.0
            }
        }
    }
}
