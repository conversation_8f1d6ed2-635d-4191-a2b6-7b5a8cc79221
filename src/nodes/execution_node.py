"""
执行节点配置 - 专门负责订单执行
"""

from typing import Dict, Any
from nautilus_trader.config import TradingNodeConfig, LoggingConfig
from nautilus_trader.model.identifiers import TraderId
from nautilus_trader.adapters.binance import BINANCE, BinanceExecClientConfig, BinanceAccountType


def create_execution_node_config(config: Dict[str, Any]) -> TradingNodeConfig:
    """创建执行节点配置"""
    
    # 基础配置
    node_name = config.get("name", "execution-node")
    trader_id = TraderId(f"{node_name.upper()}")
    
    # 执行客户端配置
    exec_clients = {}
    
    # Binance执行配置
    if "binance" in config.get("exchanges", {}):
        binance_config = config["exchanges"]["binance"]
        
        # Binance现货执行
        if binance_config.get("spot", {}).get("enabled", False):
            exec_clients["BINANCE-SPOT"] = BinanceExecClientConfig(
                api_key=binance_config["spot"]["api_key"],
                api_secret=binance_config["spot"]["api_secret"],
                account_type=BinanceAccountType.SPOT,
                base_url_http=binance_config.get("base_url_http"),
                base_url_ws=binance_config.get("base_url_ws"),
                testnet=binance_config.get("testnet", True),
                us=False,
            )

        # Binance期货执行
        if binance_config.get("futures", {}).get("enabled", False):
            exec_clients["BINANCE-FUTURES"] = BinanceExecClientConfig(
                api_key=binance_config["futures"]["api_key"],
                api_secret=binance_config["futures"]["api_secret"],
                account_type=BinanceAccountType.USDT_FUTURE,
                base_url_http=binance_config.get("base_url_http"),
                base_url_ws=binance_config.get("base_url_ws"),
                testnet=binance_config.get("testnet", True),
                us=False,
            )
    
    # TODO: 添加其他交易所执行配置
    # OKX执行配置
    if "okx" in config.get("exchanges", {}):
        pass
    
    # Bybit执行配置
    if "bybit" in config.get("exchanges", {}):
        pass
    
    # 创建节点配置
    return TradingNodeConfig(
        trader_id=trader_id,
        logging=LoggingConfig(
            log_level=config.get("log_level", "INFO"),
            log_directory=config.get("log_directory", "logs"),
            log_file_name=config.get("log_file_name", "execution_node.log"),
        ),
        data_clients={},  # 执行节点不需要数据客户端
        exec_clients=exec_clients,
        timeout_connection=config.get("timeout_connection", 10.0),
        timeout_reconciliation=config.get("timeout_reconciliation", 5.0),
        timeout_portfolio=config.get("timeout_portfolio", 5.0),
        timeout_disconnection=config.get("timeout_disconnection", 5.0),
    )


# 示例执行节点配置
EXAMPLE_EXECUTION_NODE_CONFIG = {
    "name": "main-execution-node",
    "log_level": "INFO",
    "log_to_file": True,
    "exchanges": {
        "binance": {
            "enabled": True,
            "testnet": True,
            "spot": {
                "enabled": True,
                "api_key": "your_binance_spot_api_key",
                "api_secret": "your_binance_spot_api_secret"
            },
            "futures": {
                "enabled": True,
                "api_key": "your_binance_futures_api_key",
                "api_secret": "your_binance_futures_api_secret"
            }
        }
    }
}
