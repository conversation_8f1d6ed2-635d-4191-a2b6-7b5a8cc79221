"""
数据节点配置 - 专门负责数据收集和分发
"""

from typing import Dict, Any
from nautilus_trader.config import TradingNodeConfig, LoggingConfig
from nautilus_trader.model.identifiers import TraderId
from nautilus_trader.adapters.binance import BINANCE, BinanceDataClientConfig, BinanceAccountType


def create_data_node_config(config: Dict[str, Any]) -> TradingNodeConfig:
    """创建数据节点配置"""
    
    # 基础配置
    node_name = config.get("name", "data-node")
    trader_id = TraderId(f"{node_name.upper()}")
    
    # 数据客户端配置
    data_clients = {}
    
    # Binance数据配置
    if "binance" in config.get("exchanges", {}):
        binance_config = config["exchanges"]["binance"]
        
        # Binance现货数据
        if binance_config.get("spot", {}).get("enabled", False):
            data_clients[f"{BINANCE}-SPOT"] = BinanceDataClientConfig(
                api_key=binance_config["spot"]["api_key"],
                api_secret=binance_config["spot"]["api_secret"],
                account_type=BinanceAccountType.SPOT,
                base_url_http=binance_config.get("base_url_http"),
                base_url_ws=binance_config.get("base_url_ws"),
                testnet=binance_config.get("testnet", True),
                us=False,
            )
        
        # Binance期货数据
        if binance_config.get("futures", {}).get("enabled", False):
            data_clients[f"{BINANCE}-FUTURES"] = BinanceDataClientConfig(
                api_key=binance_config["futures"]["api_key"],
                api_secret=binance_config["futures"]["api_secret"],
                account_type=BinanceAccountType.USDT_FUTURE,
                base_url_http=binance_config.get("base_url_http"),
                base_url_ws=binance_config.get("base_url_ws"),
                testnet=binance_config.get("testnet", True),
                us=False,
            )
    
    # TODO: 添加其他交易所配置
    # OKX配置
    if "okx" in config.get("exchanges", {}):
        # okx_config = config["exchanges"]["okx"]
        # data_clients[OKX] = OKXDataClientConfig(...)
        pass
    
    # Bybit配置
    if "bybit" in config.get("exchanges", {}):
        # bybit_config = config["exchanges"]["bybit"]
        # data_clients[BYBIT] = BybitDataClientConfig(...)
        pass
    
    # 新闻数据配置
    if "news" in config.get("data_sources", {}):
        # news_config = config["data_sources"]["news"]
        # data_clients[NEWS] = NewsDataClientConfig(...)
        pass
    
    # 链上数据配置
    if "onchain" in config.get("data_sources", {}):
        # onchain_config = config["data_sources"]["onchain"]
        # data_clients[ONCHAIN] = OnchainDataClientConfig(...)
        pass
    
    # 创建节点配置
    return TradingNodeConfig(
        trader_id=trader_id,
        logging=LoggingConfig(
            log_level=config.get("log_level", "INFO"),
            log_file_format=config.get("log_file_format", "{time} | {level} | {name} | {message}"),
            log_directory=config.get("log_directory", "logs"),
            log_file_name=config.get("log_file_name", "data_node.log"),
        ),
        data_clients=data_clients,
        exec_clients={},  # 数据节点不需要执行客户端
        timeout_connection=config.get("timeout_connection", 10.0),
        timeout_reconciliation=config.get("timeout_reconciliation", 5.0),
        timeout_portfolio=config.get("timeout_portfolio", 5.0),
        timeout_disconnection=config.get("timeout_disconnection", 5.0),
    )


def create_multi_exchange_data_node_config(exchanges_config: Dict[str, Any]) -> TradingNodeConfig:
    """创建多交易所数据节点配置"""
    
    data_clients = {}
    
    # 遍历所有交易所配置
    for exchange_name, exchange_config in exchanges_config.items():
        if not exchange_config.get("enabled", False):
            continue
            
        if exchange_name == "binance":
            # Binance现货
            if exchange_config.get("spot", {}).get("enabled", False):
                data_clients[f"{BINANCE}-SPOT"] = BinanceDataClientConfig(
                    api_key=exchange_config["spot"]["api_key"],
                    api_secret=exchange_config["spot"]["api_secret"],
                    account_type=BinanceAccountType.SPOT,
                    testnet=exchange_config.get("testnet", True),
                )
            
            # Binance期货
            if exchange_config.get("futures", {}).get("enabled", False):
                data_clients[f"{BINANCE}-FUTURES"] = BinanceDataClientConfig(
                    api_key=exchange_config["futures"]["api_key"],
                    api_secret=exchange_config["futures"]["api_secret"],
                    account_type=BinanceAccountType.USDT_FUTURE,
                    testnet=exchange_config.get("testnet", True),
                )
        
        # TODO: 添加其他交易所
        elif exchange_name == "okx":
            pass
        elif exchange_name == "bybit":
            pass
    
    return TradingNodeConfig(
        trader_id=TraderId("MULTI-EXCHANGE-DATA"),
        logging=LoggingConfig(log_level="INFO"),
        data_clients=data_clients,
        exec_clients={},
    )


def create_specialized_data_nodes_config() -> Dict[str, TradingNodeConfig]:
    """创建专门化的数据节点配置"""
    
    configs = {}
    
    # CEX数据节点 - 专门收集中心化交易所数据
    configs["cex_data_node"] = TradingNodeConfig(
        trader_id=TraderId("CEX-DATA-NODE"),
        logging=LoggingConfig(log_level="INFO"),
        data_clients={
            # 这里会添加所有CEX的数据客户端
        },
        exec_clients={},
    )
    
    # DEX数据节点 - 专门收集去中心化交易所数据
    configs["dex_data_node"] = TradingNodeConfig(
        trader_id=TraderId("DEX-DATA-NODE"),
        logging=LoggingConfig(log_level="INFO"),
        data_clients={
            # 这里会添加所有DEX的数据客户端
        },
        exec_clients={},
    )
    
    # 新闻数据节点 - 专门收集新闻和社交媒体数据
    configs["news_data_node"] = TradingNodeConfig(
        trader_id=TraderId("NEWS-DATA-NODE"),
        logging=LoggingConfig(log_level="INFO"),
        data_clients={
            # 这里会添加新闻数据客户端
        },
        exec_clients={},
    )
    
    # 链上数据节点 - 专门收集区块链数据
    configs["onchain_data_node"] = TradingNodeConfig(
        trader_id=TraderId("ONCHAIN-DATA-NODE"),
        logging=LoggingConfig(log_level="INFO"),
        data_clients={
            # 这里会添加链上数据客户端
        },
        exec_clients={},
    )
    
    return configs


# 示例配置
EXAMPLE_DATA_NODE_CONFIG = {
    "name": "main-data-node",
    "log_level": "INFO",
    "log_to_file": True,
    "timeout_connection": 10.0,
    "exchanges": {
        "binance": {
            "enabled": True,
            "testnet": True,
            "spot": {
                "enabled": True,
                "api_key": "your_binance_spot_api_key",
                "api_secret": "your_binance_spot_api_secret"
            },
            "futures": {
                "enabled": True,
                "api_key": "your_binance_futures_api_key",
                "api_secret": "your_binance_futures_api_secret"
            }
        },
        "okx": {
            "enabled": False,
            "api_key": "your_okx_api_key",
            "api_secret": "your_okx_api_secret",
            "passphrase": "your_okx_passphrase"
        },
        "bybit": {
            "enabled": False,
            "api_key": "your_bybit_api_key",
            "api_secret": "your_bybit_api_secret"
        }
    },
    "data_sources": {
        "news": {
            "enabled": False,
            "twitter": {
                "enabled": False,
                "bearer_token": "your_twitter_bearer_token"
            },
            "reddit": {
                "enabled": False,
                "client_id": "your_reddit_client_id",
                "client_secret": "your_reddit_client_secret"
            }
        },
        "onchain": {
            "enabled": False,
            "ethereum": {
                "enabled": False,
                "rpc_url": "your_ethereum_rpc_url"
            },
            "bsc": {
                "enabled": False,
                "rpc_url": "your_bsc_rpc_url"
            }
        }
    }
}
