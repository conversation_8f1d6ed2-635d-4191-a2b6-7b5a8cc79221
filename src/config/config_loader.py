"""
配置加载器 - 从YAML文件和环境变量加载系统配置
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional
from pathlib import Path
import re


logger = logging.getLogger(__name__)


class ConfigLoader:
    """配置加载器"""
    
    def __init__(self, config_dir: Optional[Path] = None):
        self.config_dir = config_dir or Path(__file__).parent.parent.parent / "config"
        self.env_pattern = re.compile(r'\$\{([^}]+)\}')
    
    def load_system_config(self, config_file: str = "system.yaml") -> Dict[str, Any]:
        """加载系统配置"""
        config_path = self.config_dir / config_file
        
        if not config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 替换环境变量
        config = self._substitute_env_vars(config)
        
        # 验证配置
        self._validate_config(config)
        
        logger.info(f"Loaded configuration from {config_path}")
        return config
    
    def _substitute_env_vars(self, obj: Any) -> Any:
        """递归替换配置中的环境变量"""
        if isinstance(obj, dict):
            return {key: self._substitute_env_vars(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._substitute_env_vars(item) for item in obj]
        elif isinstance(obj, str):
            return self._substitute_string_env_vars(obj)
        else:
            return obj
    
    def _substitute_string_env_vars(self, text: str) -> str:
        """替换字符串中的环境变量"""
        def replace_env_var(match):
            env_var = match.group(1)
            default_value = None
            
            # 支持默认值语法: ${VAR_NAME:default_value}
            if ':' in env_var:
                env_var, default_value = env_var.split(':', 1)
            
            value = os.getenv(env_var, default_value)
            
            if value is None:
                logger.warning(f"Environment variable {env_var} not found")
                return match.group(0)  # 返回原始字符串
            
            return value
        
        return self.env_pattern.sub(replace_env_var, text)
    
    def _validate_config(self, config: Dict[str, Any]):
        """验证配置的基本结构"""
        required_sections = ['system', 'data_nodes']
        
        for section in required_sections:
            if section not in config:
                raise ValueError(f"Missing required configuration section: {section}")
        
        # 验证系统配置
        system_config = config['system']
        required_system_fields = ['name', 'version', 'environment']
        
        for field in required_system_fields:
            if field not in system_config:
                raise ValueError(f"Missing required system field: {field}")
        
        # 验证数据节点配置
        data_nodes = config['data_nodes']
        if not isinstance(data_nodes, list) or len(data_nodes) == 0:
            raise ValueError("At least one data node must be configured")
        
        for i, node in enumerate(data_nodes):
            if 'name' not in node:
                raise ValueError(f"Data node {i} missing required field: name")
    
    def get_api_keys_from_env(self) -> Dict[str, str]:
        """从环境变量获取API密钥"""
        api_keys = {}
        
        # Binance API密钥
        binance_spot_key = os.getenv('BINANCE_SPOT_API_KEY')
        binance_spot_secret = os.getenv('BINANCE_SPOT_API_SECRET')
        binance_futures_key = os.getenv('BINANCE_FUTURES_API_KEY')
        binance_futures_secret = os.getenv('BINANCE_FUTURES_API_SECRET')
        
        if binance_spot_key and binance_spot_secret:
            api_keys['binance_spot'] = {
                'api_key': binance_spot_key,
                'api_secret': binance_spot_secret
            }
        
        if binance_futures_key and binance_futures_secret:
            api_keys['binance_futures'] = {
                'api_key': binance_futures_key,
                'api_secret': binance_futures_secret
            }
        
        # OKX API密钥
        okx_key = os.getenv('OKX_API_KEY')
        okx_secret = os.getenv('OKX_API_SECRET')
        okx_passphrase = os.getenv('OKX_PASSPHRASE')
        
        if okx_key and okx_secret and okx_passphrase:
            api_keys['okx'] = {
                'api_key': okx_key,
                'api_secret': okx_secret,
                'passphrase': okx_passphrase
            }
        
        # Bybit API密钥
        bybit_key = os.getenv('BYBIT_API_KEY')
        bybit_secret = os.getenv('BYBIT_API_SECRET')
        
        if bybit_key and bybit_secret:
            api_keys['bybit'] = {
                'api_key': bybit_key,
                'api_secret': bybit_secret
            }
        
        return api_keys
    
    def validate_api_keys(self, config: Dict[str, Any]) -> bool:
        """验证API密钥是否完整"""
        missing_keys = []
        
        # 检查数据节点的API密钥
        for node in config.get('data_nodes', []):
            exchanges = node.get('exchanges', {})
            
            for exchange_name, exchange_config in exchanges.items():
                if not exchange_config.get('enabled', False):
                    continue
                
                if exchange_name == 'binance':
                    spot_config = exchange_config.get('spot', {})
                    futures_config = exchange_config.get('futures', {})
                    
                    if spot_config.get('enabled', False):
                        if not spot_config.get('api_key') or not spot_config.get('api_secret'):
                            missing_keys.append('Binance Spot API keys')
                    
                    if futures_config.get('enabled', False):
                        if not futures_config.get('api_key') or not futures_config.get('api_secret'):
                            missing_keys.append('Binance Futures API keys')
                
                elif exchange_name == 'okx':
                    if not exchange_config.get('api_key') or not exchange_config.get('api_secret') or not exchange_config.get('passphrase'):
                        missing_keys.append('OKX API keys')
                
                elif exchange_name == 'bybit':
                    if not exchange_config.get('api_key') or not exchange_config.get('api_secret'):
                        missing_keys.append('Bybit API keys')
        
        if missing_keys:
            logger.error(f"Missing API keys: {', '.join(missing_keys)}")
            return False
        
        return True
    
    def create_env_template(self, output_file: str = ".env.template"):
        """创建环境变量模板文件"""
        template = """# 交易系统环境变量配置

# Binance API配置
BINANCE_SPOT_API_KEY=your_binance_spot_api_key_here
BINANCE_SPOT_API_SECRET=your_binance_spot_api_secret_here
BINANCE_FUTURES_API_KEY=your_binance_futures_api_key_here
BINANCE_FUTURES_API_SECRET=your_binance_futures_api_secret_here

# OKX API配置
OKX_API_KEY=your_okx_api_key_here
OKX_API_SECRET=your_okx_api_secret_here
OKX_PASSPHRASE=your_okx_passphrase_here

# Bybit API配置
BYBIT_API_KEY=your_bybit_api_key_here
BYBIT_API_SECRET=your_bybit_api_secret_here

# 新闻数据API配置
TWITTER_BEARER_TOKEN=your_twitter_bearer_token_here
REDDIT_CLIENT_ID=your_reddit_client_id_here
REDDIT_CLIENT_SECRET=your_reddit_client_secret_here

# 区块链RPC配置
ETHEREUM_RPC_URL=your_ethereum_rpc_url_here
BSC_RPC_URL=your_bsc_rpc_url_here

# 数据库配置
REDIS_PASSWORD=your_redis_password_here
POSTGRES_USERNAME=your_postgres_username_here
POSTGRES_PASSWORD=your_postgres_password_here
INFLUXDB_USERNAME=your_influxdb_username_here
INFLUXDB_PASSWORD=your_influxdb_password_here

# 通知配置
EMAIL_USERNAME=your_email_username_here
EMAIL_PASSWORD=your_email_password_here
SLACK_WEBHOOK_URL=your_slack_webhook_url_here
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here
"""
        
        output_path = self.config_dir.parent / output_file
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(template)
        
        logger.info(f"Created environment template: {output_path}")


# 全局配置加载器实例
config_loader = ConfigLoader()
