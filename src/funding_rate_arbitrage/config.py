"""
Configuration management for the funding rate arbitrage system.
"""

import os
from typing import Optional
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class BinanceConfig(BaseModel):
    """Binance API configuration."""
    
    # Spot API credentials
    api_key: str = Field(..., description="Binance spot API key")
    api_secret: str = Field(..., description="Binance spot API secret")
    
    # Futures API credentials (can be same as spot)
    futures_api_key: str = Field(..., description="Binance futures API key")
    futures_api_secret: str = Field(..., description="Binance futures API secret")
    
    # Network configuration
    testnet: bool = Field(default=True, description="Use testnet for testing")
    base_url_http: Optional[str] = Field(default=None, description="Custom HTTP base URL")
    base_url_ws: Optional[str] = Field(default=None, description="Custom WebSocket base URL")
    
    @classmethod
    def from_env(cls) -> "BinanceConfig":
        """Create configuration from environment variables."""
        return cls(
            api_key=os.getenv("BINANCE_API_KEY", ""),
            api_secret=os.getenv("BINANCE_API_SECRET", ""),
            futures_api_key=os.getenv("BINANCE_FUTURES_API_KEY", ""),
            futures_api_secret=os.getenv("BINANCE_FUTURES_API_SECRET", ""),
            testnet=os.getenv("BINANCE_TESTNET", "true").lower() == "true",
            base_url_http=os.getenv("BINANCE_BASE_URL_HTTP"),
            base_url_ws=os.getenv("BINANCE_BASE_URL_WS"),
        )


class RiskConfig(BaseModel):
    """Risk management configuration."""
    
    max_position_size: float = Field(default=1000.0, description="Maximum position size in USDT")
    max_daily_loss: float = Field(default=100.0, description="Maximum daily loss in USDT")
    funding_rate_threshold: float = Field(default=0.01, description="Minimum funding rate to trigger arbitrage")
    
    @classmethod
    def from_env(cls) -> "RiskConfig":
        """Create configuration from environment variables."""
        return cls(
            max_position_size=float(os.getenv("MAX_POSITION_SIZE", "1000")),
            max_daily_loss=float(os.getenv("MAX_DAILY_LOSS", "100")),
            funding_rate_threshold=float(os.getenv("FUNDING_RATE_THRESHOLD", "0.01")),
        )


class LoggingConfig(BaseModel):
    """Logging configuration."""
    
    level: str = Field(default="INFO", description="Log level")
    to_file: bool = Field(default=True, description="Enable file logging")
    
    @classmethod
    def from_env(cls) -> "LoggingConfig":
        """Create configuration from environment variables."""
        return cls(
            level=os.getenv("LOG_LEVEL", "INFO"),
            to_file=os.getenv("LOG_TO_FILE", "true").lower() == "true",
        )


class AppConfig(BaseModel):
    """Main application configuration."""
    
    binance: BinanceConfig
    risk: RiskConfig
    logging: LoggingConfig
    
    @classmethod
    def from_env(cls) -> "AppConfig":
        """Create configuration from environment variables."""
        return cls(
            binance=BinanceConfig.from_env(),
            risk=RiskConfig.from_env(),
            logging=LoggingConfig.from_env(),
        )
    
    def validate_api_keys(self) -> bool:
        """Validate that all required API keys are provided."""
        required_keys = [
            self.binance.api_key,
            self.binance.api_secret,
            self.binance.futures_api_key,
            self.binance.futures_api_secret,
        ]
        return all(key.strip() for key in required_keys)


# Global configuration instance
config = AppConfig.from_env()
