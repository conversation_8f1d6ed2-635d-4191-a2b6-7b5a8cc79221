"""
Binance API client for testing connections and basic operations.
"""

import asyncio
import logging
from typing import Dict, Any, Optional

from nautilus_trader.adapters.binance import BINANCE
from nautilus_trader.adapters.binance import BinanceAccountType
from nautilus_trader.adapters.binance import BinanceDataClientConfig
from nautilus_trader.adapters.binance import BinanceExecClientConfig
from nautilus_trader.adapters.binance import BinanceLiveDataClientFactory
from nautilus_trader.adapters.binance import BinanceLiveExecClientFactory
from nautilus_trader.config import TradingNodeConfig, LoggingConfig
from nautilus_trader.live.node import TradingNode
from nautilus_trader.model.identifiers import TraderId

from .config import AppConfig

logger = logging.getLogger(__name__)


class BinanceAPITester:
    """Test Binance API connections and basic functionality."""

    def __init__(self, config: AppConfig):
        self.config = config
        self._setup_logging()
        self.node = None

    def _setup_logging(self):
        """Setup logging configuration."""
        logging.basicConfig(
            level=getattr(logging, self.config.logging.level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

    def create_spot_node_config(self) -> TradingNodeConfig:
        """Create TradingNode configuration for Binance spot."""
        return TradingNodeConfig(
            trader_id=TraderId("TESTER-SPOT"),
            logging=LoggingConfig(log_level=self.config.logging.level),
            data_clients={
                BINANCE: BinanceDataClientConfig(
                    api_key=self.config.binance.api_key,
                    api_secret=self.config.binance.api_secret,
                    account_type=BinanceAccountType.SPOT,
                    base_url_http=self.config.binance.base_url_http,
                    base_url_ws=self.config.binance.base_url_ws,
                    us=False,
                    testnet=self.config.binance.testnet,
                ),
            },
            exec_clients={
                BINANCE: BinanceExecClientConfig(
                    api_key=self.config.binance.api_key,
                    api_secret=self.config.binance.api_secret,
                    account_type=BinanceAccountType.SPOT,
                    base_url_http=self.config.binance.base_url_http,
                    base_url_ws=self.config.binance.base_url_ws,
                    us=False,
                    testnet=self.config.binance.testnet,
                ),
            },
            timeout_connection=10.0,
            timeout_reconciliation=5.0,
            timeout_portfolio=5.0,
            timeout_disconnection=5.0,
        )

    def create_futures_node_config(self) -> TradingNodeConfig:
        """Create TradingNode configuration for Binance futures."""
        return TradingNodeConfig(
            trader_id=TraderId("TESTER-FUTURES"),
            logging=LoggingConfig(log_level=self.config.logging.level),
            data_clients={
                BINANCE: BinanceDataClientConfig(
                    api_key=self.config.binance.futures_api_key,
                    api_secret=self.config.binance.futures_api_secret,
                    account_type=BinanceAccountType.USDT_FUTURE,
                    base_url_http=self.config.binance.base_url_http,
                    base_url_ws=self.config.binance.base_url_ws,
                    us=False,
                    testnet=self.config.binance.testnet,
                ),
            },
            exec_clients={
                BINANCE: BinanceExecClientConfig(
                    api_key=self.config.binance.futures_api_key,
                    api_secret=self.config.binance.futures_api_secret,
                    account_type=BinanceAccountType.USDT_FUTURE,
                    base_url_http=self.config.binance.base_url_http,
                    base_url_ws=self.config.binance.base_url_ws,
                    us=False,
                    testnet=self.config.binance.testnet,
                ),
            },
            timeout_connection=10.0,
            timeout_reconciliation=5.0,
            timeout_portfolio=5.0,
            timeout_disconnection=5.0,
        )
    
    async def test_spot_connection(self) -> Dict[str, Any]:
        """Test Binance spot API connection using TradingNode."""
        logger.info("Testing Binance spot API connection...")

        try:
            # Create node configuration
            config = self.create_spot_node_config()

            # Create and configure the trading node
            node = TradingNode(config=config)
            node.add_data_client_factory(BINANCE, BinanceLiveDataClientFactory)
            node.add_exec_client_factory(BINANCE, BinanceLiveExecClientFactory)

            # Build the node (this will test connections)
            node.build()

            # Test if we can access the clients
            data_clients = node.kernel.data_engine.registered_clients
            exec_clients = node.kernel.exec_engine.registered_clients

            if not data_clients or not exec_clients:
                raise Exception("Failed to create Binance clients")

            # Check if BINANCE clients are registered
            binance_data_found = any(BINANCE in str(client) for client in data_clients)
            binance_exec_found = any(BINANCE in str(client) for client in exec_clients)

            if not binance_data_found or not binance_exec_found:
                raise Exception("Binance clients not found in registered clients")

            logger.info("✅ Binance spot API connection successful")

            # Clean up
            node.dispose()

            return {
                "status": "success",
                "message": "Binance spot API connection successful",
                "account_type": "SPOT",
                "testnet": self.config.binance.testnet
            }

        except Exception as e:
            logger.error(f"❌ Binance spot API connection failed: {e}")
            return {
                "status": "error",
                "message": f"Binance spot API connection failed: {e}",
                "account_type": "SPOT",
                "testnet": self.config.binance.testnet
            }

    async def test_futures_connection(self) -> Dict[str, Any]:
        """Test Binance futures API connection using TradingNode."""
        logger.info("Testing Binance futures API connection...")

        try:
            # Create node configuration
            config = self.create_futures_node_config()

            # Create and configure the trading node
            node = TradingNode(config=config)
            node.add_data_client_factory(BINANCE, BinanceLiveDataClientFactory)
            node.add_exec_client_factory(BINANCE, BinanceLiveExecClientFactory)

            # Build the node (this will test connections)
            node.build()

            # Test if we can access the clients
            data_clients = node.kernel.data_engine.registered_clients
            exec_clients = node.kernel.exec_engine.registered_clients

            if not data_clients or not exec_clients:
                raise Exception("Failed to create Binance clients")

            # Check if BINANCE clients are registered
            binance_data_found = any(BINANCE in str(client) for client in data_clients)
            binance_exec_found = any(BINANCE in str(client) for client in exec_clients)

            if not binance_data_found or not binance_exec_found:
                raise Exception("Binance clients not found in registered clients")

            logger.info("✅ Binance futures API connection successful")

            # Clean up
            node.dispose()

            return {
                "status": "success",
                "message": "Binance futures API connection successful",
                "account_type": "USDT_FUTURE",
                "testnet": self.config.binance.testnet
            }

        except Exception as e:
            logger.error(f"❌ Binance futures API connection failed: {e}")
            return {
                "status": "error",
                "message": f"Binance futures API connection failed: {e}",
                "account_type": "USDT_FUTURE",
                "testnet": self.config.binance.testnet
            }
    
    async def test_all_connections(self) -> Dict[str, Any]:
        """Test all Binance API connections."""
        logger.info("Starting comprehensive Binance API connection tests...")

        results = {
            "spot": await self.test_spot_connection(),
            "futures": await self.test_futures_connection(),
        }

        # Summary
        all_successful = all(result["status"] == "success" for result in results.values())

        summary = {
            "overall_status": "success" if all_successful else "partial_failure",
            "testnet": self.config.binance.testnet,
            "results": results,
            "summary": {
                "total_tests": len(results),
                "successful": sum(1 for r in results.values() if r["status"] == "success"),
                "failed": sum(1 for r in results.values() if r["status"] == "error"),
            }
        }

        if all_successful:
            logger.info("🎉 All Binance API connections successful!")
        else:
            logger.warning("⚠️ Some Binance API connections failed")

        return summary
    
def main():
    """Main function to run API tests."""
    from .config import config

    # Validate configuration
    if not config.validate_api_keys():
        logger.error("❌ Invalid API configuration. Please check your .env file.")
        return

    async def run_tests():
        # Create tester and run tests
        tester = BinanceAPITester(config)
        results = await tester.test_all_connections()

        # Print results
        print("\n" + "="*60)
        print("BINANCE API CONNECTION TEST RESULTS")
        print("="*60)
        print(f"Overall Status: {results['overall_status'].upper()}")
        print(f"Testnet Mode: {results['testnet']}")
        print(f"Successful: {results['summary']['successful']}/{results['summary']['total_tests']}")
        print("\nDetailed Results:")
        for test_name, result in results['results'].items():
            status_icon = "✅" if result['status'] == 'success' else "❌"
            print(f"  {status_icon} {test_name.upper()}: {result['message']}")
        print("="*60)

    # Run the async tests
    asyncio.run(run_tests())


if __name__ == "__main__":
    main()
