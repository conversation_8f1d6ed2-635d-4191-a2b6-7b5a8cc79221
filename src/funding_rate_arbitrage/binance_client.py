"""
Binance API client for testing connections and basic operations.
"""

import asyncio
import logging
import aiohttp
import hmac
import hashlib
import time
from typing import Dict, Any, Optional
from urllib.parse import urlencode

from .config import AppConfig

logger = logging.getLogger(__name__)


class BinanceAPITester:
    """Test Binance API connections and basic functionality."""

    def __init__(self, config: AppConfig):
        self.config = config
        self._setup_logging()

        # Set base URLs based on testnet configuration
        if self.config.binance.testnet:
            self.spot_base_url = "https://testnet.binance.vision"
            self.futures_base_url = "https://testnet.binancefuture.com"
        else:
            self.spot_base_url = "https://api.binance.com"
            self.futures_base_url = "https://fapi.binance.com"

    def _setup_logging(self):
        """Setup logging configuration."""
        logging.basicConfig(
            level=getattr(logging, self.config.logging.level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

    def _generate_signature(self, query_string: str, secret: str) -> str:
        """Generate HMAC SHA256 signature for Binance API."""
        return hmac.new(
            secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

    async def _make_request(self, url: str, params: Dict[str, Any] = None,
                           headers: Dict[str, str] = None) -> Dict[str, Any]:
        """Make HTTP request to Binance API."""
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(url, params=params, headers=headers, timeout=10) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        error_text = await response.text()
                        return {
                            "error": f"HTTP {response.status}: {error_text}"
                        }
            except asyncio.TimeoutError:
                return {"error": "Request timeout"}
            except Exception as e:
                return {"error": f"Request failed: {str(e)}"}
    
    async def test_spot_server_time(self) -> Dict[str, Any]:
        """Test Binance spot server time endpoint (no authentication required)."""
        url = f"{self.spot_base_url}/api/v3/time"
        result = await self._make_request(url)

        if "error" in result:
            return {
                "status": "error",
                "message": f"Spot server time test failed: {result['error']}",
                "endpoint": "GET /api/v3/time"
            }

        return {
            "status": "success",
            "message": "Spot server time test successful",
            "endpoint": "GET /api/v3/time",
            "server_time": result.get("serverTime")
        }

    async def test_futures_server_time(self) -> Dict[str, Any]:
        """Test Binance futures server time endpoint (no authentication required)."""
        url = f"{self.futures_base_url}/fapi/v1/time"
        result = await self._make_request(url)

        if "error" in result:
            return {
                "status": "error",
                "message": f"Futures server time test failed: {result['error']}",
                "endpoint": "GET /fapi/v1/time"
            }

        return {
            "status": "success",
            "message": "Futures server time test successful",
            "endpoint": "GET /fapi/v1/time",
            "server_time": result.get("serverTime")
        }

    async def test_spot_account_info(self) -> Dict[str, Any]:
        """Test Binance spot account info endpoint (requires authentication)."""
        timestamp = int(time.time() * 1000)
        query_string = f"timestamp={timestamp}"
        signature = self._generate_signature(query_string, self.config.binance.api_secret)

        url = f"{self.spot_base_url}/api/v3/account"
        params = {
            "timestamp": timestamp,
            "signature": signature
        }
        headers = {
            "X-MBX-APIKEY": self.config.binance.api_key
        }

        result = await self._make_request(url, params, headers)

        if "error" in result:
            return {
                "status": "error",
                "message": f"Spot account info test failed: {result['error']}",
                "endpoint": "GET /api/v3/account"
            }

        return {
            "status": "success",
            "message": "Spot account info test successful",
            "endpoint": "GET /api/v3/account",
            "account_type": "SPOT"
        }

    async def test_futures_account_info(self) -> Dict[str, Any]:
        """Test Binance futures account info endpoint (requires authentication)."""
        timestamp = int(time.time() * 1000)
        query_string = f"timestamp={timestamp}"
        signature = self._generate_signature(query_string, self.config.binance.futures_api_secret)

        url = f"{self.futures_base_url}/fapi/v2/account"
        params = {
            "timestamp": timestamp,
            "signature": signature
        }
        headers = {
            "X-MBX-APIKEY": self.config.binance.futures_api_key
        }

        result = await self._make_request(url, params, headers)

        if "error" in result:
            return {
                "status": "error",
                "message": f"Futures account info test failed: {result['error']}",
                "endpoint": "GET /fapi/v2/account"
            }

        return {
            "status": "success",
            "message": "Futures account info test successful",
            "endpoint": "GET /fapi/v2/account",
            "account_type": "USDT_FUTURE"
        }
    
    async def test_spot_connection(self) -> Dict[str, Any]:
        """Test Binance spot API connection."""
        logger.info("Testing Binance spot API connection...")
        
        try:
            # Create data client
            data_config = self.create_spot_data_config()
            data_client = BinanceLiveDataClientFactory.create(
                loop=asyncio.get_event_loop(),
                name="BINANCE_SPOT_DATA",
                config=data_config,
                msgbus=self.msgbus,
                cache=self.cache,
                clock=self.clock,
            )

            # Create execution client
            exec_config = self.create_spot_exec_config()
            exec_client = BinanceLiveExecClientFactory.create(
                loop=asyncio.get_event_loop(),
                name="BINANCE_SPOT_EXEC",
                config=exec_config,
                msgbus=self.msgbus,
                cache=self.cache,
                clock=self.clock,
            )
            
            # Connect clients
            await data_client.connect()
            await exec_client.connect()
            
            logger.info("✅ Binance spot API connection successful")
            
            # Disconnect clients
            await data_client.disconnect()
            await exec_client.disconnect()
            
            return {
                "status": "success",
                "message": "Binance spot API connection successful",
                "account_type": "SPOT",
                "testnet": self.config.binance.testnet
            }
            
        except Exception as e:
            logger.error(f"❌ Binance spot API connection failed: {e}")
            return {
                "status": "error",
                "message": f"Binance spot API connection failed: {e}",
                "account_type": "SPOT",
                "testnet": self.config.binance.testnet
            }
    
    async def test_futures_connection(self) -> Dict[str, Any]:
        """Test Binance futures API connection."""
        logger.info("Testing Binance futures API connection...")
        
        try:
            # Create data client
            data_config = self.create_futures_data_config()
            data_client = BinanceLiveDataClientFactory.create(
                loop=asyncio.get_event_loop(),
                name="BINANCE_FUTURES_DATA",
                config=data_config,
                msgbus=self.msgbus,
                cache=self.cache,
                clock=self.clock,
            )

            # Create execution client
            exec_config = self.create_futures_exec_config()
            exec_client = BinanceLiveExecClientFactory.create(
                loop=asyncio.get_event_loop(),
                name="BINANCE_FUTURES_EXEC",
                config=exec_config,
                msgbus=self.msgbus,
                cache=self.cache,
                clock=self.clock,
            )
            
            # Connect clients
            await data_client.connect()
            await exec_client.connect()
            
            logger.info("✅ Binance futures API connection successful")
            
            # Disconnect clients
            await data_client.disconnect()
            await exec_client.disconnect()
            
            return {
                "status": "success",
                "message": "Binance futures API connection successful",
                "account_type": "USDT_FUTURE",
                "testnet": self.config.binance.testnet
            }
            
        except Exception as e:
            logger.error(f"❌ Binance futures API connection failed: {e}")
            return {
                "status": "error",
                "message": f"Binance futures API connection failed: {e}",
                "account_type": "USDT_FUTURE",
                "testnet": self.config.binance.testnet
            }
    
    async def test_all_connections(self) -> Dict[str, Any]:
        """Test all Binance API connections."""
        logger.info("Starting comprehensive Binance API connection tests...")
        
        results = {
            "spot": await self.test_spot_connection(),
            "futures": await self.test_futures_connection(),
        }
        
        # Summary
        all_successful = all(result["status"] == "success" for result in results.values())
        
        summary = {
            "overall_status": "success" if all_successful else "partial_failure",
            "testnet": self.config.binance.testnet,
            "results": results,
            "summary": {
                "total_tests": len(results),
                "successful": sum(1 for r in results.values() if r["status"] == "success"),
                "failed": sum(1 for r in results.values() if r["status"] == "error"),
            }
        }
        
        if all_successful:
            logger.info("🎉 All Binance API connections successful!")
        else:
            logger.warning("⚠️ Some Binance API connections failed")
        
        return summary


async def main():
    """Main function to run API tests."""
    from .config import config
    
    # Validate configuration
    if not config.validate_api_keys():
        logger.error("❌ Invalid API configuration. Please check your .env file.")
        return
    
    # Create tester and run tests
    tester = BinanceAPITester(config)
    results = await tester.test_all_connections()
    
    # Print results
    print("\n" + "="*60)
    print("BINANCE API CONNECTION TEST RESULTS")
    print("="*60)
    print(f"Overall Status: {results['overall_status'].upper()}")
    print(f"Testnet Mode: {results['testnet']}")
    print(f"Successful: {results['summary']['successful']}/{results['summary']['total_tests']}")
    print("\nDetailed Results:")
    for test_name, result in results['results'].items():
        status_icon = "✅" if result['status'] == 'success' else "❌"
        print(f"  {status_icon} {test_name.upper()}: {result['message']}")
    print("="*60)


if __name__ == "__main__":
    asyncio.run(main())
