"""
Binance API client for testing connections and basic operations.
"""

import asyncio
import logging
from typing import Dict, Any, Optional

from nautilus_trader.adapters.binance import BINANCE
from nautilus_trader.adapters.binance import BinanceAccountType
from nautilus_trader.adapters.binance import BinanceDataClientConfig
from nautilus_trader.adapters.binance import BinanceExecClientConfig
from nautilus_trader.adapters.binance import BinanceLiveDataClientFactory
from nautilus_trader.adapters.binance import BinanceLiveExecClientFactory
from nautilus_trader.config import TradingNodeConfig, LoggingConfig
from nautilus_trader.live.node import TradingNode
from nautilus_trader.model.identifiers import TraderId

from .config import AppConfig

logger = logging.getLogger(__name__)


class BinanceAPITester:
    """Test Binance API connections and basic functionality."""

    def __init__(self, config: AppConfig):
        self.config = config
        self._setup_logging()
        self.node = None

    def _setup_logging(self):
        """Setup logging configuration."""
        logging.basicConfig(
            level=getattr(logging, self.config.logging.level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

    def create_spot_node_config(self) -> TradingNodeConfig:
        """Create TradingNode configuration for Binance spot."""
        return TradingNodeConfig(
            trader_id=TraderId("TESTER-SPOT"),
            logging=LoggingConfig(log_level=self.config.logging.level),
            data_clients={
                BINANCE: BinanceDataClientConfig(
                    api_key=self.config.binance.api_key,
                    api_secret=self.config.binance.api_secret,
                    account_type=BinanceAccountType.SPOT,
                    base_url_http=self.config.binance.base_url_http,
                    base_url_ws=self.config.binance.base_url_ws,
                    us=False,
                    testnet=self.config.binance.testnet,
                ),
            },
            exec_clients={
                BINANCE: BinanceExecClientConfig(
                    api_key=self.config.binance.api_key,
                    api_secret=self.config.binance.api_secret,
                    account_type=BinanceAccountType.SPOT,
                    base_url_http=self.config.binance.base_url_http,
                    base_url_ws=self.config.binance.base_url_ws,
                    us=False,
                    testnet=self.config.binance.testnet,
                ),
            },
            timeout_connection=10.0,
            timeout_reconciliation=5.0,
            timeout_portfolio=5.0,
            timeout_disconnection=5.0,
        )

    def create_futures_node_config(self) -> TradingNodeConfig:
        """Create TradingNode configuration for Binance futures."""
        return TradingNodeConfig(
            trader_id=TraderId("TESTER-FUTURES"),
            logging=LoggingConfig(log_level=self.config.logging.level),
            data_clients={
                BINANCE: BinanceDataClientConfig(
                    api_key=self.config.binance.futures_api_key,
                    api_secret=self.config.binance.futures_api_secret,
                    account_type=BinanceAccountType.USDT_FUTURE,
                    base_url_http=self.config.binance.base_url_http,
                    base_url_ws=self.config.binance.base_url_ws,
                    us=False,
                    testnet=self.config.binance.testnet,
                ),
            },
            exec_clients={
                BINANCE: BinanceExecClientConfig(
                    api_key=self.config.binance.futures_api_key,
                    api_secret=self.config.binance.futures_api_secret,
                    account_type=BinanceAccountType.USDT_FUTURE,
                    base_url_http=self.config.binance.base_url_http,
                    base_url_ws=self.config.binance.base_url_ws,
                    us=False,
                    testnet=self.config.binance.testnet,
                ),
            },
            timeout_connection=10.0,
            timeout_reconciliation=5.0,
            timeout_portfolio=5.0,
            timeout_disconnection=5.0,
        )
    
    async def test_spot_connection(self) -> Dict[str, Any]:
        """Test Binance spot API connection using TradingNode."""
        logger.info("Testing Binance spot API connection...")

        try:
            # Create node configuration
            config = self.create_spot_node_config()

            # Create and configure the trading node
            node = TradingNode(config=config)
            node.add_data_client_factory(BINANCE, BinanceLiveDataClientFactory)
            node.add_exec_client_factory(BINANCE, BinanceLiveExecClientFactory)

            # Build the node (this will test connections)
            node.build()

            # Test if we can access the clients
            data_clients = node.kernel.data_engine.registered_clients
            exec_clients = node.kernel.exec_engine.registered_clients

            if not data_clients or not exec_clients:
                raise Exception("Failed to create Binance clients")

            # Check if BINANCE clients are registered
            binance_data_found = any(BINANCE in str(client) for client in data_clients)
            binance_exec_found = any(BINANCE in str(client) for client in exec_clients)

            if not binance_data_found or not binance_exec_found:
                raise Exception("Binance clients not found in registered clients")

            logger.info("✅ Binance spot API connection successful")

            # Clean up
            node.dispose()

            return {<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 1855.650390625 382" style="max-width: 1855.650390625px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572"><style>#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .error-icon{fill:#a44141;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .edge-thickness-normal{stroke-width:1px;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .marker.cross{stroke:lightgrey;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 p{margin:0;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .cluster-label text{fill:#F9FFFE;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .cluster-label span{color:#F9FFFE;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .cluster-label span p{background-color:transparent;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .label text,#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 span{fill:#ccc;color:#ccc;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .node rect,#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .node circle,#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .node ellipse,#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .node polygon,#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .rough-node .label text,#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .node .label text,#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .image-shape .label,#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .icon-shape .label{text-anchor:middle;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .rough-node .label,#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .node .label,#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .image-shape .label,#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .icon-shape .label{text-align:center;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .node.clickable{cursor:pointer;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .arrowheadPath{fill:lightgrey;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .cluster text{fill:#F9FFFE;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .cluster span{color:#F9FFFE;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 rect.text{fill:none;stroke-width:0;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .icon-shape,#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .icon-shape p,#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .icon-shape rect,#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TN_K_0" d="M1083.74,62L1083.74,66.167C1083.74,70.333,1083.74,78.667,1083.74,86.333C1083.74,94,1083.74,101,1083.74,104.5L1083.74,108"></path><path marker-end="url(#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_DE_1" d="M1030.818,142.234L897.806,150.361C764.794,158.489,498.769,174.745,365.757,186.372C232.744,198,232.744,205,232.744,208.5L232.744,212"></path><path marker-end="url(#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_EE_2" d="M1030.818,146.692L980.008,154.076C929.197,161.461,827.576,176.231,776.766,187.115C725.955,198,725.955,205,725.955,208.5L725.955,212"></path><path marker-end="url(#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_RE_3" d="M1030.818,155.228L1011.375,161.19C991.932,167.152,953.045,179.076,933.602,188.538C914.158,198,914.158,205,914.158,208.5L914.158,212"></path><path marker-end="url(#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_C_4" d="M1083.74,166L1083.74,170.167C1083.74,174.333,1083.74,182.667,1083.74,190.333C1083.74,198,1083.74,205,1083.74,208.5L1083.74,212"></path><path marker-end="url(#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_MB_5" d="M1136.662,154.915L1156.661,160.929C1176.661,166.943,1216.66,178.972,1236.659,188.486C1256.658,198,1256.658,205,1256.658,208.5L1256.658,212"></path><path marker-end="url(#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_P_6" d="M1136.662,146.752L1187.008,154.127C1237.354,161.501,1338.045,176.251,1388.391,187.125C1438.736,198,1438.736,205,1438.736,208.5L1438.736,212"></path><path marker-end="url(#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_T_7" d="M1136.662,144.068L1218.34,151.89C1300.018,159.712,1463.373,175.356,1545.051,186.678C1626.729,198,1626.729,205,1626.729,208.5L1626.729,212"></path><path marker-end="url(#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DE_DC1_8" d="M168.756,270L158.881,274.167C149.007,278.333,129.257,286.667,119.383,294.333C109.508,302,109.508,309,109.508,312.5L109.508,316"></path><path marker-end="url(#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DE_DC2_9" d="M296.732,270L306.607,274.167C316.482,278.333,336.231,286.667,346.106,294.333C355.98,302,355.98,309,355.98,312.5L355.98,316"></path><path marker-end="url(#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EE_EC1_10" d="M661.898,270L652.013,274.167C642.127,278.333,622.357,286.667,612.471,294.333C602.586,302,602.586,309,602.586,312.5L602.586,316"></path><path marker-end="url(#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EE_EC2_11" d="M796.26,266.77L810.176,271.475C824.092,276.18,851.925,285.59,865.841,293.795C879.758,302,879.758,309,879.758,312.5L879.758,316"></path><path marker-end="url(#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_T_S1_12" d="M1573.721,258.481L1552.881,264.568C1532.041,270.654,1490.361,282.827,1469.521,292.414C1448.682,302,1448.682,309,1448.682,312.5L1448.682,316"></path><path marker-end="url(#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_T_S2_13" d="M1626.729,270L1626.729,274.167C1626.729,278.333,1626.729,286.667,1626.729,294.333C1626.729,302,1626.729,309,1626.729,312.5L1626.729,316"></path><path marker-end="url(#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_T_A1_14" d="M1679.736,259.459L1698.814,265.382C1717.891,271.306,1756.046,283.153,1775.124,292.576C1794.201,302,1794.201,309,1794.201,312.5L1794.201,316"></path><path marker-end="url(#mermaid-2a9f5cea-e9c4-4eea-858b-912ff1837572_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MB_ALL_15" d="M1256.658,270L1256.658,274.167C1256.658,278.333,1256.658,286.667,1256.658,294.333C1256.658,302,1256.658,309,1256.658,312.5L1256.658,316"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(1083.740234375, 35)" id="flowchart-TN-330" class="node default"><rect height="54" width="148.84375" y="-27" x="-74.421875" style="" class="basic label-container"></rect><g transform="translate(-44.421875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="88.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>TradingNode</p></span></div></foreignObject></g></g><g transform="translate(1083.740234375, 139)" id="flowchart-K-331" class="node default"><rect height="54" width="105.84375" y="-27" x="-52.921875" style="fill:#ff9999 !important" class="basic label-container"></rect><g transform="translate(-22.921875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="45.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Kernel</p></span></div></foreignObject></g></g><g transform="translate(232.744140625, 243)" id="flowchart-DE-333" class="node default"><rect height="54" width="140.34375" y="-27" x="-70.171875" style="fill:#99ccff !important" class="basic label-container"></rect><g transform="translate(-40.171875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>DataEngine</p></span></div></foreignObject></g></g><g transform="translate(725.955078125, 243)" id="flowchart-EE-335" class="node default"><rect height="54" width="140.609375" y="-27" x="-70.3046875" style="fill:#99ff99 !important" class="basic label-container"></rect><g transform="translate(-40.3046875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ExecEngine</p></span></div></foreignObject></g></g><g transform="translate(914.158203125, 243)" id="flowchart-RE-337" class="node default"><rect height="54" width="135.796875" y="-27" x="-67.8984375" style="fill:#ffcc99 !important" class="basic label-container"></rect><g transform="translate(-37.8984375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="75.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>RiskEngine</p></span></div></foreignObject></g></g><g transform="translate(1083.740234375, 243)" id="flowchart-C-339" class="node default"><rect height="54" width="103.3671875" y="-27" x="-51.68359375" style="" class="basic label-container"></rect><g transform="translate(-21.68359375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="43.3671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Cache</p></span></div></foreignObject></g></g><g transform="translate(1256.658203125, 243)" id="flowchart-MB-341" class="node default"><rect height="54" width="142.46875" y="-27" x="-71.234375" style="" class="basic label-container"></rect><g transform="translate(-41.234375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="82.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>MessageBus</p></span></div></foreignObject></g></g><g transform="translate(1438.736328125, 243)" id="flowchart-P-343" class="node default"><rect height="54" width="121.6875" y="-27" x="-60.84375" style="" class="basic label-container"></rect><g transform="translate(-30.84375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="61.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Portfolio</p></span></div></foreignObject></g></g><g transform="translate(1626.728515625, 243)" id="flowchart-T-345" class="node default"><rect height="54" width="106.015625" y="-27" x="-53.0078125" style="" class="basic label-container"></rect><g transform="translate(-23.0078125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="46.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Trader</p></span></div></foreignObject></g></g><g transform="translate(109.5078125, 347)" id="flowchart-DC1-347" class="node default"><rect height="54" width="203.015625" y="-27" x="-101.5078125" style="" class="basic label-container"></rect><g transform="translate(-71.5078125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="143.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>DataClient-BINANCE</p></span></div></foreignObject></g></g><g transform="translate(355.98046875, 347)" id="flowchart-DC2-349" class="node default"><rect height="54" width="189.9296875" y="-27" x="-94.96484375" style="" class="basic label-container"></rect><g transform="translate(-64.96484375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="129.9296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>DataClient-OTHER</p></span></div></foreignObject></g></g><g transform="translate(602.5859375, 347)" id="flowchart-EC1-351" class="node default"><rect height="54" width="203.28125" y="-27" x="-101.640625" style="" class="basic label-container"></rect><g transform="translate(-71.640625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="143.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ExecClient-BINANCE</p></span></div></foreignObject></g></g><g transform="translate(879.7578125, 347)" id="flowchart-EC2-353" class="node default"><rect height="54" width="190.1953125" y="-27" x="-95.09765625" style="" class="basic label-container"></rect><g transform="translate(-65.09765625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="130.1953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ExecClient-OTHER</p></span></div></foreignObject></g></g><g transform="translate(1448.681640625, 347)" id="flowchart-S1-355" class="node default"><rect height="54" width="128.046875" y="-27" x="-64.0234375" style="" class="basic label-container"></rect><g transform="translate(-34.0234375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="68.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Strategy1</p></span></div></foreignObject></g></g><g transform="translate(1626.728515625, 347)" id="flowchart-S2-357" class="node default"><rect height="54" width="128.046875" y="-27" x="-64.0234375" style="" class="basic label-container"></rect><g transform="translate(-34.0234375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="68.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Strategy2</p></span></div></foreignObject></g></g><g transform="translate(1794.201171875, 347)" id="flowchart-A1-359" class="node default"><rect height="54" width="106.8984375" y="-27" x="-53.44921875" style="" class="basic label-container"></rect><g transform="translate(-23.44921875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="46.8984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Actor1</p></span></div></foreignObject></g></g><g transform="translate(1256.658203125, 347)" id="flowchart-ALL-361" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>所有组件通信</p></span></div></foreignObject></g></g></g></g></g></svg>
                "status": "success",
                "message": "Binance spot API connection successful",
                "account_type": "SPOT",
                "testnet": self.config.binance.testnet
            }

        except Exception as e:
            logger.error(f"❌ Binance spot API connection failed: {e}")
            return {
                "status": "error",
                "message": f"Binance spot API connection failed: {e}",
                "account_type": "SPOT",
                "testnet": self.config.binance.testnet
            }

    async def test_futures_connection(self) -> Dict[str, Any]:
        """Test Binance futures API connection using TradingNode."""
        logger.info("Testing Binance futures API connection...")

        try:
            # Create node configuration
            config = self.create_futures_node_config()

            # Create and configure the trading node
            node = TradingNode(config=config)
            node.add_data_client_factory(BINANCE, BinanceLiveDataClientFactory)
            node.add_exec_client_factory(BINANCE, BinanceLiveExecClientFactory)

            # Build the node (this will test connections)
            node.build()

            # Test if we can access the clients
            data_clients = node.kernel.data_engine.registered_clients
            exec_clients = node.kernel.exec_engine.registered_clients

            if not data_clients or not exec_clients:
                raise Exception("Failed to create Binance clients")

            # Check if BINANCE clients are registered
            binance_data_found = any(BINANCE in str(client) for client in data_clients)
            binance_exec_found = any(BINANCE in str(client) for client in exec_clients)

            if not binance_data_found or not binance_exec_found:
                raise Exception("Binance clients not found in registered clients")

            logger.info("✅ Binance futures API connection successful")

            # Clean up
            node.dispose()

            return {
                "status": "success",
                "message": "Binance futures API connection successful",
                "account_type": "USDT_FUTURE",
                "testnet": self.config.binance.testnet
            }

        except Exception as e:
            logger.error(f"❌ Binance futures API connection failed: {e}")
            return {
                "status": "error",
                "message": f"Binance futures API connection failed: {e}",
                "account_type": "USDT_FUTURE",
                "testnet": self.config.binance.testnet
            }
    
    async def test_all_connections(self) -> Dict[str, Any]:
        """Test all Binance API connections."""
        logger.info("Starting comprehensive Binance API connection tests...")

        results = {
            "spot": await self.test_spot_connection(),
            "futures": await self.test_futures_connection(),
        }

        # Summary
        all_successful = all(result["status"] == "success" for result in results.values())

        summary = {
            "overall_status": "success" if all_successful else "partial_failure",
            "testnet": self.config.binance.testnet,
            "results": results,
            "summary": {
                "total_tests": len(results),
                "successful": sum(1 for r in results.values() if r["status"] == "success"),
                "failed": sum(1 for r in results.values() if r["status"] == "error"),
            }
        }

        if all_successful:
            logger.info("🎉 All Binance API connections successful!")
        else:
            logger.warning("⚠️ Some Binance API connections failed")

        return summary
    
def main():
    """Main function to run API tests."""
    from .config import config

    # Validate configuration
    if not config.validate_api_keys():
        logger.error("❌ Invalid API configuration. Please check your .env file.")
        return

    async def run_tests():
        # Create tester and run tests
        tester = BinanceAPITester(config)
        results = await tester.test_all_connections()

        # Print results
        print("\n" + "="*60)
        print("BINANCE API CONNECTION TEST RESULTS")
        print("="*60)
        print(f"Overall Status: {results['overall_status'].upper()}")
        print(f"Testnet Mode: {results['testnet']}")
        print(f"Successful: {results['summary']['successful']}/{results['summary']['total_tests']}")
        print("\nDetailed Results:")
        for test_name, result in results['results'].items():
            status_icon = "✅" if result['status'] == 'success' else "❌"
            print(f"  {status_icon} {test_name.upper()}: {result['message']}")
        print("="*60)

    # Run the async tests
    asyncio.run(run_tests())


if __name__ == "__main__":
    main()
