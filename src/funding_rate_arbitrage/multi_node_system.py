"""
资金费率套利系统的多节点架构
"""

import asyncio
from typing import Dict, List
from nautilus_trader.config import TradingNodeConfig, LoggingConfig
from nautilus_trader.live.node import TradingNode
from nautilus_trader.model.identifiers import TraderId
from nautilus_trader.adapters.binance import BINANCE, BinanceDataClientConfig, BinanceExecClientConfig
from nautilus_trader.adapters.binance import BinanceAccountType

from .config import AppConfig


class FundingRateArbitrageSystem:
    """资金费率套利多节点系统"""
    
    def __init__(self, config: AppConfig):
        self.config = config
        self.nodes: Dict[str, TradingNode] = {}
    
    def create_data_collection_node(self) -> TradingNode:
        """创建数据收集节点 - 专门收集资金费率和价格数据"""
        config = TradingNodeConfig(
            trader_id=TraderId("FUNDING-DATA-COLLECTOR"),
            logging=LoggingConfig(log_level=self.config.logging.level),
            data_clients={
                BINANCE: BinanceDataClientConfig(
                    api_key=self.config.binance.api_key,
                    api_secret=self.config.binance.api_secret,
                    account_type=BinanceAccountType.USDT_FUTURE,
                    testnet=self.config.binance.testnet,
                ),
            },
            exec_clients={},  # 数据节点不执行交易
        )
        return TradingNode(config=config)
    
    def create_spot_trading_node(self) -> TradingNode:
        """创建现货交易节点"""
        config = TradingNodeConfig(
            trader_id=TraderId("SPOT-TRADER"),
            logging=LoggingConfig(log_level=self.config.logging.level),
            data_clients={
                BINANCE: BinanceDataClientConfig(
                    api_key=self.config.binance.api_key,
                    api_secret=self.config.binance.api_secret,
                    account_type=BinanceAccountType.SPOT,
                    testnet=self.config.binance.testnet,
                ),
            },
            exec_clients={
                BINANCE: BinanceExecClientConfig(
                    api_key=self.config.binance.api_key,
                    api_secret=self.config.binance.api_secret,
                    account_type=BinanceAccountType.SPOT,
                    testnet=self.config.binance.testnet,
                ),
            },
        )
        return TradingNode(config=config)
    
    def create_futures_trading_node(self) -> TradingNode:
        """创建期货交易节点"""
        config = TradingNodeConfig(
            trader_id=TraderId("FUTURES-TRADER"),
            logging=LoggingConfig(log_level=self.config.logging.level),
            data_clients={
                BINANCE: BinanceDataClientConfig(
                    api_key=self.config.binance.futures_api_key,
                    api_secret=self.config.binance.futures_api_secret,
                    account_type=BinanceAccountType.USDT_FUTURE,
                    testnet=self.config.binance.testnet,
                ),
            },
            exec_clients={
                BINANCE: BinanceExecClientConfig(
                    api_key=self.config.binance.futures_api_key,
                    api_secret=self.config.binance.futures_api_secret,
                    account_type=BinanceAccountType.USDT_FUTURE,
                    testnet=self.config.binance.testnet,
                ),
            },
        )
        return TradingNode(config=config)
    
    def create_arbitrage_strategy_node(self) -> TradingNode:
        """创建套利策略节点"""
        config = TradingNodeConfig(
            trader_id=TraderId("ARBITRAGE-STRATEGY"),
            logging=LoggingConfig(log_level=self.config.logging.level),
            # 策略节点从共享数据源获取数据
            data_clients={},
            exec_clients={},
        )
        return TradingNode(config=config)
    
    def create_risk_management_node(self) -> TradingNode:
        """创建风险管理节点"""
        config = TradingNodeConfig(
            trader_id=TraderId("RISK-MANAGER"),
            logging=LoggingConfig(log_level=self.config.logging.level),
            data_clients={},
            exec_clients={},
        )
        return TradingNode(config=config)
    
    async def build_system(self):
        """构建整个系统"""
        print("🏗️ 构建资金费率套利多节点系统...")
        
        # 创建所有节点
        self.nodes = {
            "data_collector": self.create_data_collection_node(),
            "spot_trader": self.create_spot_trading_node(),
            "futures_trader": self.create_futures_trading_node(),
            "arbitrage_strategy": self.create_arbitrage_strategy_node(),
            "risk_manager": self.create_risk_management_node(),
        }
        
        # 构建所有节点
        for name, node in self.nodes.items():
            print(f"🔧 构建节点: {name}")
            node.build()
        
        print("✅ 系统构建完成！")
    
    async def start_system(self):
        """启动系统 - 按依赖顺序启动"""
        print("🚀 启动资金费率套利系统...")
        
        # 1. 首先启动数据收集节点
        print("📊 启动数据收集节点...")
        await self.nodes["data_collector"].start_async()
        
        # 2. 启动风险管理节点
        print("🛡️ 启动风险管理节点...")
        await self.nodes["risk_manager"].start_async()
        
        # 3. 并行启动交易节点
        print("⚡ 启动交易节点...")
        await asyncio.gather(
            self.nodes["spot_trader"].start_async(),
            self.nodes["futures_trader"].start_async(),
        )
        
        # 4. 最后启动策略节点
        print("🎯 启动套利策略节点...")
        await self.nodes["arbitrage_strategy"].start_async()
        
        print("✅ 所有节点启动完成！")
        print("🔄 资金费率套利系统正在运行...")
    
    async def stop_system(self):
        """停止系统 - 按相反顺序停止"""
        print("🛑 停止资金费率套利系统...")
        
        stop_order = [
            "arbitrage_strategy",
            "futures_trader", 
            "spot_trader",
            "risk_manager",
            "data_collector"
        ]
        
        for name in stop_order:
            if name in self.nodes:
                print(f"⏹️ 停止节点: {name}")
                await self.nodes[name].stop_async()
        
        print("✅ 所有节点已停止！")
    
    def get_node_status(self) -> Dict[str, str]:
        """获取所有节点状态"""
        status = {}
        for name, node in self.nodes.items():
            status[name] = "running" if node.is_running else "stopped"
        return status


async def main():
    """主函数示例"""
    from .config import config
    
    system = FundingRateArbitrageSystem(config)
    
    try:
        await system.build_system()
        await system.start_system()
        
        # 显示系统状态
        print("\n📊 系统状态:")
        for name, status in system.get_node_status().items():
            print(f"  {name}: {status}")
        
        # 运行一段时间
        print("\n⏰ 系统运行中...")
        await asyncio.sleep(30)
        
    except KeyboardInterrupt:
        print("\n⚠️ 收到停止信号...")
    finally:
        await system.stop_system()


if __name__ == "__main__":
    asyncio.run(main())
